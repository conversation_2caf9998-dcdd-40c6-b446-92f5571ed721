import { Metadata } from "next";
import { languages } from "./languages";
import { getTranslations } from "./translations";
import { getTranslatedRoute } from "./route-translations";

/**
 * Génère les métadonnées pour une page avec les routes traduites correctement
 * 
 * @param lang Code de langue
 * @param route Route originale en anglais (ex: 'countdown', 'timer', etc.) ou '' pour la page d'accueil
 * @returns Métadonnées avec les routes traduites
 */
export function generatePageMetadata(lang: string, route: string = ''): Metadata {
  const t = getTranslations(lang);
  
  // Déterminer le titre et la description en fonction de la route
  let title = t.homePageTitle;
  let description = t.homePageDescription;
  let keywords = "online time management, free timer, stopwatch online, countdown timer, productivity tools";
  
  if (route) {
    switch (route) {
      case 'timer':
        title = t.timerPageTitle;
        description = t.timerPageDescription;
        keywords = "online timer, stopwatch, free timer, time tracking, lap timer";
        break;
      case 'countdown':
        title = t.countdownPageTitle;
        description = t.countdownPageDescription;
        keywords = "online countdown timer, free countdown, timer online, countdown app, time management";
        break;
      case 'todo':
        title = t.todoPageTitle;
        description = t.todoPageDescription;
        keywords = "online todo list, task manager, free todo app, task organizer, productivity tool";
        break;
      case 'time-tracking':
        title = t.timeTrackingPageTitle;
        description = t.timeTrackingPageDescription;
        keywords = "online time tracking, free time tracker, time management, productivity tool, work hours";
        break;
      case 'world-clock':
        title = t.worldClockPageTitle;
        description = t.worldClockPageDescription;
        keywords = "online world clock, time zones, international time, global clock, time converter";
        break;
      case 'intervals':
        title = t.intervalsPageTitle;
        description = t.intervalsPageDescription;
        keywords = "online interval timer, HIIT timer, workout intervals, interval training, fitness timer";
        break;
      case 'pomodoro':
        title = t.pomodoroPageTitle;
        description = t.pomodoroPageDescription;
        keywords = "online pomodoro timer, productivity technique, focus timer, work sessions, time management";
        break;
      case 'meeting-timer':
        title = t.meetingTimerPageTitle;
        description = t.meetingTimerPageDescription;
        keywords = "online meeting timer, meeting cost calculator, agenda timer, professional meetings, time management";
        break;
      case 'time-billing':
        title = t.timeBillingPageTitle;
        description = t.timeBillingPageDescription;
        keywords = "online time billing, freelance hours, billable time, invoice calculator, professional tool";
        break;
      case 'workout-intervals':
        title = t.workoutIntervalsPageTitle;
        description = t.workoutIntervalsPageDescription;
        keywords = "online workout timer, interval training, HIIT workouts, fitness timer, exercise intervals";
        break;
      case 'exercise-templates':
        title = t.exerciseTemplatesPageTitle;
        description = t.exerciseTemplatesPageDescription;
        keywords = "online workout planner, exercise templates, fitness routines, workout organizer, training plans";
        break;
    }
  }
  
  // Générer les liens hreflang pour toutes les langues supportées
  const languageAlternates: Record<string, string> = {};
  languages.forEach((language) => {
    if (route) {
      // Pour les pages de fonctionnalités, utiliser la route traduite
      const translatedRoute = getTranslatedRoute(language.code, route);
      languageAlternates[language.code] = `https://smarttime.pro/${language.code}/${translatedRoute}`;
    } else {
      // Pour la page d'accueil, utiliser simplement le code de langue
      languageAlternates[language.code] = `https://smarttime.pro/${language.code}`;
    }
  });
  
  // Construire l'URL canonique
  let canonicalUrl = `https://smarttime.pro/${lang}`;
  if (route) {
    const translatedRoute = getTranslatedRoute(lang, route);
    canonicalUrl = `${canonicalUrl}/${translatedRoute}`;
  }
  
  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      url: canonicalUrl,
      type: 'website',
    },
    alternates: {
      canonical: canonicalUrl,
      languages: languageAlternates
    }
  };
}
