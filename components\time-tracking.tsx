"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { getTranslations } from "@/lib/i18n/translations"
import {
  Play,
  Pause,
  Trash2,
  Clock,
  CalendarIcon,
  BarChart3,
  Edit,
  Save,
  X,
  Plus,
  Download,
  Search,
} from "lucide-react"
import { format } from "date-fns"
import { ToolControls } from "@/components/tool-controls"
import { ToolFullscreenWrapper } from "@/components/tool-fullscreen-wrapper"
import {
  fr, enUS, es, de, it, pt, nl, pl, uk, tr, ru, ar, he,
  id, ms, th, vi, zhCN as zh, ja, ko, el, bg, cs, sk, hu, ro,
  hr, sr, bs, sl, et, lv, lt, da, fi, nb, sv, ca, af
} from "date-fns/locale"

// Fonctions utilitaires pour le stockage local
const saveToLocalStorage = <T,>(key: string, data: T): void => {
  try {
    if (typeof window !== "undefined") {
      const serializedData = JSON.stringify(data)
      localStorage.setItem(key, serializedData)
    }
  } catch (error) {
    console.error("Error saving to localStorage:", error)
  }
}

const getFromLocalStorage = <T,>(key: string, defaultValue: T): T => {
  try {
    if (typeof window !== "undefined") {
      const serializedData = localStorage.getItem(key)
      if (serializedData === null) {
        return defaultValue
      }
      return JSON.parse(serializedData) as T
    }
    return defaultValue
  } catch (error) {
    console.error("Error getting from localStorage:", error)
    return defaultValue
  }
}

// Vérifier si une langue est RTL
const isRTL = (lang: string): boolean => {
  return ["ar", "he"].includes(lang);
}

interface TimeTrackingProps {
  lang: string
}

type Category = "work" | "personal" | "meeting" | "learning" | "other"

interface TimeEntry {
  id: string
  description: string
  category: Category
  tags: string[]
  startTime: string // ISO string
  endTime: string | null // ISO string or null if ongoing
  duration: number // in milliseconds
  date: string // YYYY-MM-DD
  notes?: string
}

const STORAGE_KEY = "timetools_time_entries"
const ACTIVE_ENTRY_KEY = "timetools_active_entry"

export function TimeTracking({ lang }: TimeTrackingProps) {
  const translationsRef = useRef(getTranslations(lang));
  const t = translationsRef.current;
  const isRTLLayout = isRTL(lang);
  const [entries, setEntries] = useState<TimeEntry[]>([])
  const [activeEntry, setActiveEntry] = useState<TimeEntry | null>(null)
  const [newDescription, setNewDescription] = useState("")
  const [newCategory, setNewCategory] = useState<Category>("work")
  const [newTags, setNewTags] = useState<string[]>([])
  const [newTagInput, setNewTagInput] = useState("")
  const [currentTime, setCurrentTime] = useState(Date.now())
  const [activeTab, setActiveTab] = useState("tracker")
  const [searchQuery, setSearchQuery] = useState("")
  const [categoryFilter, setCategoryFilter] = useState<Category | "all">("all")
  const [dateFilter, setDateFilter] = useState<Date | null>(null)
  const [editingEntry, setEditingEntry] = useState<string | null>(null)
  const [editDescription, setEditDescription] = useState("")
  const [editCategory, setEditCategory] = useState<Category>("work")
  const [editTags, setEditTags] = useState<string[]>([])
  const [editTagInput, setEditTagInput] = useState("")
  const [editNotes, setEditNotes] = useState("")
  const [isToolFullscreen, setIsToolFullscreen] = useState(false)

  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const originalTitleRef = useRef<string>("")

  // Locale pour date-fns
  const getLocale = () => {
    switch (lang) {
      case "fr": return fr;
      case "es": return es;
      case "de": return de;
      case "it": return it;
      case "pt": return pt;
      case "nl": return nl;
      case "pl": return pl;
      case "uk": return uk;
      case "tr": return tr;
      case "ru": return ru;
      case "ar": return ar;
      case "he": return he;
      case "id": return id;
      case "ms": return ms;
      case "th": return th;
      case "vi": return vi;
      case "zh": return zh;
      case "ja": return ja;
      case "ko": return ko;
      case "el": return el;
      case "bg": return bg;
      case "cs": return cs;
      case "sk": return sk;
      case "hu": return hu;
      case "ro": return ro;
      case "hr": return hr;
      case "sr": return sr;
      case "bs": return bs;
      case "sl": return sl;
      case "et": return et;
      case "lv": return lv;
      case "lt": return lt;
      case "da": return da;
      case "fi": return fi;
      case "nb": return nb;
      case "sv": return sv;
      case "ca": return ca;
      case "af": return af;
      default: return enUS;
    }
  }

  // Charger les entrées depuis localStorage
  useEffect(() => {
    const savedEntries = getFromLocalStorage<TimeEntry[]>(STORAGE_KEY, [])
    if (savedEntries.length > 0) {
      setEntries(savedEntries)
    } else {
      // Exemples d'entrées
      const now = new Date()
      const yesterday = new Date(now)
      yesterday.setDate(yesterday.getDate() - 1)

      setEntries([
        {
          id: "1",
          description: t.exampleEntry1 || "Développement frontend",
          category: "work",
          tags: ["React", "UI"],
          startTime: new Date(now.getTime() - 7200000).toISOString(), // 2 heures avant
          endTime: new Date(now.getTime() - 3600000).toISOString(), // 1 heure avant
          duration: 3600000, // 1 heure
          date: format(now, "yyyy-MM-dd"),
          notes: "",
        },
        {
          id: "2",
          description: t.exampleEntry2 || "Réunion d'équipe",
          category: "meeting",
          tags: ["Équipe", "Planification"],
          startTime: new Date(yesterday.getTime() - 5400000).toISOString(), // 1.5 heures avant hier
          endTime: new Date(yesterday.getTime() - 3600000).toISOString(), // 1 heure avant hier
          duration: 1800000, // 30 minutes
          date: format(yesterday, "yyyy-MM-dd"),
          notes: t.exampleNotes || "Discuté des prochaines fonctionnalités",
        },
      ])
    }

    // Charger l'entrée active
    const savedActiveEntry = getFromLocalStorage<TimeEntry | null>(ACTIVE_ENTRY_KEY, null)
    if (savedActiveEntry) {
      setActiveEntry(savedActiveEntry)
    }
  }, [t])

  // Sauvegarder les entrées dans localStorage
  useEffect(() => {
    if (entries.length > 0) {
      saveToLocalStorage(STORAGE_KEY, entries)
    }
  }, [entries])

  // Sauvegarder l'entrée active dans localStorage
  useEffect(() => {
    if (activeEntry) {
      saveToLocalStorage(ACTIVE_ENTRY_KEY, activeEntry)
    } else {
      localStorage.removeItem(ACTIVE_ENTRY_KEY)
    }
  }, [activeEntry])

  // Sauvegarder le titre original de la page au chargement du composant
  useEffect(() => {
    originalTitleRef.current = document.title
    return () => {
      document.title = originalTitleRef.current
    }
  }, [])

  // Mettre à jour le temps actuel pour l'entrée active
  useEffect(() => {
    if (activeEntry) {
      timerRef.current = setInterval(() => {
        setCurrentTime(Date.now())
      }, 1000)
    } else if (timerRef.current) {
      clearInterval(timerRef.current)
      // Restaurer le titre original quand le chronomètre est arrêté
      document.title = originalTitleRef.current
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
    }
  }, [activeEntry])

  // Mettre à jour le titre de l'onglet avec le temps actif
  useEffect(() => {
    if (activeEntry) {
      const duration = getActiveDuration()
      const formattedDuration = formatDuration(duration)
      const shortDescription = activeEntry.description.length > 20
        ? activeEntry.description.substring(0, 20) + '...'
        : activeEntry.description

      document.title = `${formattedDuration} - ${shortDescription}`
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentTime, activeEntry])

  const startNewEntry = () => {
    if (newDescription.trim() === "") return

    // Arrêter l'entrée active si elle existe
    if (activeEntry) {
      stopEntry()
    }

    const now = new Date()
    const entry: TimeEntry = {
      id: Date.now().toString(),
      description: newDescription,
      category: newCategory,
      tags: [...newTags],
      startTime: now.toISOString(),
      endTime: null,
      duration: 0,
      date: format(now, "yyyy-MM-dd"),
      notes: "",
    }

    setActiveEntry(entry)
    setEntries([entry, ...entries])
    setNewDescription("")
    setNewTags([])
  }

  const stopEntry = () => {
    if (!activeEntry) return

    const now = new Date()
    const startTime = new Date(activeEntry.startTime)
    const duration = now.getTime() - startTime.getTime()

    const updatedEntry: TimeEntry = {
      ...activeEntry,
      endTime: now.toISOString(),
      duration,
    }

    setEntries(entries.map((entry) => (entry.id === activeEntry.id ? updatedEntry : entry)))
    setActiveEntry(null)

    // Restaurer le titre original
    document.title = originalTitleRef.current
  }

  const deleteEntry = (id: string) => {
    if (activeEntry && activeEntry.id === id) {
      setActiveEntry(null)
      // Restaurer le titre original si l'entrée active est supprimée
      document.title = originalTitleRef.current
    }

    setEntries(entries.filter((entry) => entry.id !== id))
  }

  const startEditing = (entry: TimeEntry) => {
    setEditingEntry(entry.id)
    setEditDescription(entry.description)
    setEditCategory(entry.category)
    setEditTags([...entry.tags])
    setEditNotes(entry.notes || "")
  }

  const saveEdit = (id: string) => {
    setEntries(
      entries.map((entry) =>
        entry.id === id
          ? {
              ...entry,
              description: editDescription,
              category: editCategory,
              tags: [...editTags],
              notes: editNotes,
            }
          : entry,
      ),
    )
    setEditingEntry(null)
  }

  const cancelEdit = () => {
    setEditingEntry(null)
  }

  const addTag = () => {
    if (newTagInput.trim() === "") return
    if (!newTags.includes(newTagInput.trim())) {
      setNewTags([...newTags, newTagInput.trim()])
    }
    setNewTagInput("")
  }

  const removeTag = (tag: string) => {
    setNewTags(newTags.filter((t) => t !== tag))
  }

  const addEditTag = () => {
    if (editTagInput.trim() === "") return
    if (!editTags.includes(editTagInput.trim())) {
      setEditTags([...editTags, editTagInput.trim()])
    }
    setEditTagInput("")
  }

  const removeEditTag = (tag: string) => {
    setEditTags(editTags.filter((t) => t !== tag))
  }

  const handleKeyDown = (e: React.KeyboardEvent, action: () => void) => {
    if (e.key === "Enter") {
      action()
    }
  }

  const formatDuration = (ms: number) => {
    const seconds = Math.floor((ms / 1000) % 60)
    const minutes = Math.floor((ms / (1000 * 60)) % 60)
    const hours = Math.floor(ms / (1000 * 60 * 60))

    return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`
  }

  const getActiveDuration = () => {
    if (!activeEntry) return 0
    const startTime = new Date(activeEntry.startTime)
    return currentTime - startTime.getTime()
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return format(date, "PPP", { locale: getLocale() })
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return format(date, "p", { locale: getLocale() })
  }

  const getCategoryColor = (category: Category) => {
    switch (category) {
      case "work":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
      case "personal":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
      case "meeting":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
      case "learning":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
      case "other":
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200"
    }
  }

  const getCategoryLabel = (category: Category) => {
    switch (category) {
      case "work":
        return t.workCategory
      case "personal":
        return t.personalCategory
      case "meeting":
        return t.meetingCategory
      case "learning":
        return t.learningCategory
      case "other":
        return t.otherCategory
    }
  }

  // Filtrer les entrées
  const filteredEntries = entries.filter((entry) => {
    // Filtre de recherche
    const matchesSearch =
      entry.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (entry.notes && entry.notes.toLowerCase().includes(searchQuery.toLowerCase())) ||
      entry.tags.some((tag) => tag.toLowerCase().includes(searchQuery.toLowerCase()))

    // Filtre de catégorie
    const matchesCategory = categoryFilter === "all" || entry.category === categoryFilter

    // Filtre de date
    const matchesDate = !dateFilter || entry.date === format(dateFilter, "yyyy-MM-dd")

    return matchesSearch && matchesCategory && matchesDate
  })

  // Calculer les statistiques
  const calculateStats = () => {
    const totalDuration = entries.reduce((total, entry) => total + entry.duration, 0)
    const activeDuration = activeEntry ? getActiveDuration() : 0
    const totalWithActive = totalDuration + activeDuration

    const categoryStats = {} as Record<Category, number>
    const tagStats = {} as Record<string, number>
    const dateStats = {} as Record<string, number>

    // Initialiser les catégories
    ;["work", "personal", "meeting", "learning", "other"].forEach((cat) => {
      categoryStats[cat as Category] = 0
    })

    // Calculer les statistiques par catégorie et par tag
    entries.forEach((entry) => {
      categoryStats[entry.category] += entry.duration

      entry.tags.forEach((tag) => {
        if (!tagStats[tag]) tagStats[tag] = 0
        tagStats[tag] += entry.duration
      })

      if (!dateStats[entry.date]) dateStats[entry.date] = 0
      dateStats[entry.date] += entry.duration
    })

    // Ajouter l'entrée active aux statistiques
    if (activeEntry) {
      categoryStats[activeEntry.category] += activeDuration

      activeEntry.tags.forEach((tag) => {
        if (!tagStats[tag]) tagStats[tag] = 0
        tagStats[tag] += activeDuration
      })

      if (!dateStats[activeEntry.date]) dateStats[activeEntry.date] = 0
      dateStats[activeEntry.date] += activeDuration
    }

    return {
      totalDuration: totalWithActive,
      categoryStats,
      tagStats,
      dateStats,
    }
  }

  const stats = calculateStats()

  // Exporter les données
  const exportData = () => {
    const dataStr = JSON.stringify(entries, null, 2)
    const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`

    const exportFileDefaultName = `time-tracking-export-${format(new Date(), "yyyy-MM-dd")}.json`

    const linkElement = document.createElement("a")
    linkElement.setAttribute("href", dataUri)
    linkElement.setAttribute("download", exportFileDefaultName)
    linkElement.click()
  }

  // Grouper les entrées par date
  const entriesByDate = filteredEntries.reduce(
    (groups, entry) => {
      if (!groups[entry.date]) {
        groups[entry.date] = []
      }
      groups[entry.date].push(entry)
      return groups
    },
    {} as Record<string, TimeEntry[]>,
  )

  // Trier les dates par ordre décroissant
  const sortedDates = Object.keys(entriesByDate).sort((a, b) => {
    return new Date(b).getTime() - new Date(a).getTime()
  })

  const mainContent = (
    <>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="tracker" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              {t.tracker || "Tracker"}
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              {t.history || "History"}
            </TabsTrigger>
            <TabsTrigger value="stats" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              {t.statistics || "Statistics"}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="tracker" className="space-y-6">
            <div className="mb-6">
              <div className="flex flex-col gap-4 mb-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="md:col-span-2">
                    <Label htmlFor="task-description" className="mb-2 block">
                      {t.whatAreYouDoing || "What are you doing?"}
                    </Label>
                    <Input
                      id="task-description"
                      placeholder={t.taskDescription || "Task description"}
                      value={newDescription}
                      onChange={(e) => setNewDescription(e.target.value)}
                      onKeyDown={(e) => handleKeyDown(e, startNewEntry)}
                      disabled={!!activeEntry}
                    />
                  </div>
                  <div>
                    <Label htmlFor="task-category" className="mb-2 block">
                      {t.category || "Category"}
                    </Label>
                    <Select
                      value={newCategory}
                      onValueChange={(value) => setNewCategory(value as Category)}
                      disabled={!!activeEntry}
                    >
                      <SelectTrigger id="task-category">
                        <SelectValue placeholder={t.selectCategory || "Select category"} />
                      </SelectTrigger>
                      <SelectContent side="bottom" align={isRTLLayout ? "end" : "start"}>
                        <SelectItem value="work">{t.workCategory || "Work"}</SelectItem>
                        <SelectItem value="personal">{t.personalCategory || "Personal"}</SelectItem>
                        <SelectItem value="meeting">{t.meetingCategory || "Meeting"}</SelectItem>
                        <SelectItem value="learning">{t.learningCategory || "Learning"}</SelectItem>
                        <SelectItem value="other">{t.otherCategory || "Other"}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <div className="flex justify-between">
                      <Label htmlFor="task-tags" className="mb-2 block">
                        {t.tags}
                      </Label>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={addTag}
                        disabled={!!activeEntry || newTagInput.trim() === ""}
                        className="h-6 px-2"
                      >
                        <Plus className="h-3 w-3 mr-1" />
                        {t.add}
                      </Button>
                    </div>
                    <div className="flex gap-2">
                      <Input
                        id="task-tags"
                        placeholder={t.addTags}
                        value={newTagInput}
                        onChange={(e) => setNewTagInput(e.target.value)}
                        onKeyDown={(e) => handleKeyDown(e, addTag)}
                        disabled={!!activeEntry}
                        className="flex-1"
                      />
                    </div>
                    {newTags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {newTags.map((tag) => (
                          <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                            {tag}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeTag(tag)}
                              disabled={!!activeEntry}
                              className="h-4 w-4 p-0 ml-1"
                            >
                              <X className="h-3 w-3" />
                              <span className="sr-only">{t.remove || "Remove"}</span>
                            </Button>
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex justify-end">
                  {activeEntry ? (
                    <Button onClick={stopEntry} variant="destructive">
                      <Pause className="h-4 w-4 mr-2" />
                      {t.stop || "Stop"}
                    </Button>
                  ) : (
                    <Button onClick={startNewEntry} disabled={newDescription.trim() === ""}>
                      <Play className="h-4 w-4 mr-2" />
                      {t.start || "Start"}
                    </Button>
                  )}
                </div>
              </div>

              {activeEntry && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="p-4 border rounded-md bg-primary/5 mt-4"
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{activeEntry.description}</h3>
                        <Badge variant="outline" className={getCategoryColor(activeEntry.category)}>
                          {getCategoryLabel(activeEntry.category)}
                        </Badge>
                      </div>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {activeEntry.tags.map((tag) => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {t.startedAt || "Started at"} {formatTime(activeEntry.startTime)}
                      </p>
                    </div>
                    <div className="text-3xl md:text-4xl font-mono font-bold">{formatDuration(getActiveDuration())}</div>
                  </div>
                </motion.div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="history">
            <div className="space-y-6">
              <div className="flex flex-col sm:flex-row gap-2 mb-4">
                <div className="relative flex-1">
                  <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder={t.searchEntries}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-8"
                  />
                </div>

                <div className="flex gap-2">
                  <Select value={categoryFilter} onValueChange={(value) => setCategoryFilter(value as any)}>
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder={t.allCategories} />
                    </SelectTrigger>
                    <SelectContent side="bottom" align={isRTLLayout ? "end" : "start"}>
                      <SelectItem value="all">{t.allCategories}</SelectItem>
                      <SelectItem value="work">{t.workCategory}</SelectItem>
                      <SelectItem value="personal">{t.personalCategory}</SelectItem>
                      <SelectItem value="meeting">{t.meetingCategory}</SelectItem>
                      <SelectItem value="learning">{t.learningCategory}</SelectItem>
                      <SelectItem value="other">{t.otherCategory}</SelectItem>
                    </SelectContent>
                  </Select>

                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-[150px] justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dateFilter ? (
                          format(dateFilter, "PPP", { locale: getLocale() })
                        ) : (
                          <span>{t.allDates}</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" side="bottom" align={isRTLLayout ? "end" : "start"}>
                      <Calendar
                        mode="single"
                        required
                        selected={dateFilter || undefined}
                        onSelect={setDateFilter}
                        initialFocus
                        lang={lang}
                      />
                      {dateFilter && (
                        <div className="p-3 border-t">
                          <Button variant="ghost" size="sm" onClick={() => setDateFilter(null)} className="w-full">
                            {t.clearDate}
                          </Button>
                        </div>
                      )}
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              <div className="flex justify-between items-center mb-4">
                <div className="text-sm text-muted-foreground">
                  {filteredEntries.length} {filteredEntries.length === 1 ? t.entry || "entry" : t.entries || "entries"}
                </div>
                <Button variant="outline" size="sm" onClick={exportData}>
                  <Download className="h-4 w-4 mr-2" />
                  {t.export || "Export"}
                </Button>
              </div>

              {filteredEntries.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground border rounded-md">
                  {t.noEntriesFound || "No entries found"}
                </div>
              ) : (
                <div className="space-y-6">
                  {sortedDates.map((date) => (
                    <div key={date} className="space-y-3">
                      <h3 className="font-medium text-sm text-muted-foreground">{formatDate(date)}</h3>
                      <div className="space-y-3">
                        {entriesByDate[date].map((entry) => (
                          <AnimatePresence key={entry.id} mode="wait">
                            {editingEntry === entry.id ? (
                              <motion.div
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                exit={{ opacity: 0 }}
                                className="p-4 border rounded-md"
                              >
                                <div className="space-y-4">
                                  <div className="flex justify-between">
                                    <h4 className="font-medium">{t.editEntry || "Edit Entry"}</h4>
                                    <div className="flex gap-2">
                                      <Button variant="ghost" size="sm" onClick={() => saveEdit(entry.id)}>
                                        <Save className="h-4 w-4 mr-1" />
                                        {t.save || "Save"}
                                      </Button>
                                      <Button variant="ghost" size="sm" onClick={cancelEdit}>
                                        <X className="h-4 w-4 mr-1" />
                                        {t.cancel || "Cancel"}
                                      </Button>
                                    </div>
                                  </div>

                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                      <Label htmlFor="edit-description" className="mb-2 block">
                                        {t.description || "Description"}
                                      </Label>
                                      <Input
                                        id="edit-description"
                                        value={editDescription}
                                        onChange={(e) => setEditDescription(e.target.value)}
                                      />
                                    </div>
                                    <div>
                                      <Label htmlFor="edit-category" className="mb-2 block">
                                        {t.category || "Category"}
                                      </Label>
                                      <Select
                                        value={editCategory}
                                        onValueChange={(value) => setEditCategory(value as Category)}
                                      >
                                        <SelectTrigger id="edit-category">
                                          <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent side="bottom" align={isRTLLayout ? "end" : "start"}>
                                          <SelectItem value="work">{t.workCategory || "Work"}</SelectItem>
                                          <SelectItem value="personal">{t.personalCategory || "Personal"}</SelectItem>
                                          <SelectItem value="meeting">{t.meetingCategory || "Meeting"}</SelectItem>
                                          <SelectItem value="learning">{t.learningCategory || "Learning"}</SelectItem>
                                          <SelectItem value="other">{t.otherCategory || "Other"}</SelectItem>
                                        </SelectContent>
                                      </Select>
                                    </div>
                                  </div>

                                  <div>
                                    <div className="flex justify-between">
                                      <Label htmlFor="edit-tags" className="mb-2 block">
                                        {t.tags || "Tags"}
                                      </Label>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={addEditTag}
                                        disabled={editTagInput.trim() === ""}
                                        className="h-6 px-2"
                                      >
                                        <Plus className="h-3 w-3 mr-1" />
                                        {t.add || "Add"}
                                      </Button>
                                    </div>
                                    <div className="flex gap-2">
                                      <Input
                                        id="edit-tags"
                                        placeholder={t.addTags || "Add tags"}
                                        value={editTagInput}
                                        onChange={(e) => setEditTagInput(e.target.value)}
                                        onKeyDown={(e) => handleKeyDown(e, addEditTag)}
                                        className="flex-1"
                                      />
                                    </div>
                                    {editTags.length > 0 && (
                                      <div className="flex flex-wrap gap-1 mt-2">
                                        {editTags.map((tag) => (
                                          <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                                            {tag}
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={() => removeEditTag(tag)}
                                              className="h-4 w-4 p-0 ml-1"
                                            >
                                              <X className="h-3 w-3" />
                                              <span className="sr-only">{t.remove}</span>
                                            </Button>
                                          </Badge>
                                        ))}
                                      </div>
                                    )}
                                  </div>

                                  <div>
                                    <Label htmlFor="edit-notes" className="mb-2 block">
                                      {t.notes || "Notes"}
                                    </Label>
                                    <Input
                                      id="edit-notes"
                                      placeholder={t.addNotes || "Add notes"}
                                      value={editNotes}
                                      onChange={(e) => setEditNotes(e.target.value)}
                                    />
                                  </div>
                                </div>
                              </motion.div>
                            ) : (
                              <motion.div
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                exit={{ opacity: 0 }}
                                className="p-4 border rounded-md"
                              >
                                <div className="flex justify-between items-start">
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-1">
                                      <h4 className="font-medium">{entry.description}</h4>
                                      <Badge variant="outline" className={getCategoryColor(entry.category)}>
                                        {getCategoryLabel(entry.category)}
                                      </Badge>
                                    </div>

                                    {entry.tags.length > 0 && (
                                      <div className="flex flex-wrap gap-1 mb-2">
                                        {entry.tags.map((tag) => (
                                          <Badge key={tag} variant="secondary" className="text-xs">
                                            {tag}
                                          </Badge>
                                        ))}
                                      </div>
                                    )}

                                    {entry.notes && <p className="text-sm text-muted-foreground mb-2">{entry.notes}</p>}

                                    <div className="flex items-center gap-3 text-sm text-muted-foreground">
                                      <span className="flex items-center gap-1">
                                        <Clock className="h-3 w-3" />
                                        {formatTime(entry.startTime)} -{" "}
                                        {entry.endTime ? formatTime(entry.endTime) : t.inProgress || "In progress"}
                                      </span>
                                      <span className="font-mono">{formatDuration(entry.duration)}</span>
                                    </div>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      onClick={() => startEditing(entry)}
                                      className="h-8 w-8"
                                    >
                                      <Edit className="h-4 w-4" />
                                      <span className="sr-only">{t.edit || "Edit"}</span>
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      onClick={() => deleteEntry(entry.id)}
                                      className="h-8 w-8 text-destructive"
                                    >
                                      <Trash2 className="h-4 w-4" />
                                      <span className="sr-only">{t.delete || "Delete"}</span>
                                    </Button>
                                  </div>
                                </div>
                              </motion.div>
                            )}
                          </AnimatePresence>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="stats">
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">{t.totalTrackedTime || "Total Tracked Time"}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatDuration(stats.totalDuration)}</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">{t.entriesCount || "Entries Count"}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{entries.length}</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">{t.averageDuration || "Average Duration"}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {entries.length > 0 ? formatDuration(stats.totalDuration / entries.length) : formatDuration(0)}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">{t.timeByCategory || "Time by Category"}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {Object.entries(stats.categoryStats)
                        .filter(([_, duration]) => duration > 0)
                        .sort(([_, a], [__, b]) => b - a)
                        .map(([category, duration]) => {
                          const percentage = stats.totalDuration > 0 ? (duration / stats.totalDuration) * 100 : 0
                          return (
                            <div key={category} className="space-y-1">
                              <div className="flex justify-between text-sm">
                                <span className="flex items-center gap-2">
                                  <Badge variant="outline" className={getCategoryColor(category as Category)}>
                                    {getCategoryLabel(category as Category)}
                                  </Badge>
                                </span>
                                <span className="font-mono">{formatDuration(duration)}</span>
                              </div>
                              <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                                <div className="h-full bg-primary" style={{ width: `${percentage}%` }} />
                              </div>
                            </div>
                          )
                        })}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">{t.timeByTag || "Time by Tag"}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {Object.entries(stats.tagStats)
                        .filter(([_, duration]) => duration > 0)
                        .sort(([_, a], [__, b]) => b - a)
                        .slice(0, 5) // Top 5 tags
                        .map(([tag, duration]) => {
                          const percentage = stats.totalDuration > 0 ? (duration / stats.totalDuration) * 100 : 0
                          return (
                            <div key={tag} className="space-y-1">
                              <div className="flex justify-between text-sm">
                                <span className="flex items-center gap-2">
                                  <Badge variant="secondary">{tag}</Badge>
                                </span>
                                <span className="font-mono">{formatDuration(duration)}</span>
                              </div>
                              <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                                <div className="h-full bg-primary" style={{ width: `${percentage}%` }} />
                              </div>
                            </div>
                          )
                        })}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-6">
        <div className="text-sm text-muted-foreground">{t.timeTrackingFooter || "Track your time efficiently"}</div>
        <Button variant="outline" size="sm" onClick={exportData}>
          <Download className="h-4 w-4 mr-2" />
          {t.exportData || "Export Data"}
        </Button>
      </CardFooter>
    </>
  )

  return (
    <>
      <Card className="w-full max-w-6xl mx-auto">
        <CardHeader>
          <div className="flex justify-between items-start">
            <CardTitle>{t.timeTracking || "Time Tracking"}</CardTitle>
            {/* Contrôles d'outils en haut à droite */}
            <ToolControls
              onFullscreenToggle={setIsToolFullscreen}
              variant="compact"
            />
          </div>
        </CardHeader>
        {mainContent}
      </Card>

      {/* Wrapper plein écran */}
      <ToolFullscreenWrapper
        isFullscreen={isToolFullscreen}
        onClose={() => setIsToolFullscreen(false)}
        toolName={t.timeTracking || "Time Tracking"}
      >
        <Card className="w-full border-0 shadow-none bg-transparent">
          {mainContent}
        </Card>
      </ToolFullscreenWrapper>
    </>
  )
}
