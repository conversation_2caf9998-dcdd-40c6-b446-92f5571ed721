import type React from "react"
import type { Metada<PERSON> } from "next"
import { languages, isRtlLang } from "@/lib/i18n/languages"
import { getTranslations } from "@/lib/i18n/translations"
import { Footer } from "@/components/footer"

export async function generateStaticParams() {
  return languages.map((lang) => ({
    lang: lang.code,
  }))
}

export async function generateMetadata({ params: paramsInput }: { params: { lang: string } }): Promise<Metadata> {
  // Pour satisfaire l'exigence de Next.js "params should be awaited"
  // await sur une non-promesse retourne la valeur elle-même.
  const params = await paramsInput;
  const lang = params.lang;
  const t = getTranslations(lang);
  // console.log(`[generateMetadata] Lang: ${lang}, Title: ${t.homePageTitle}`);

  // Generate hreflang alternates for all supported languages
  const languageAlternates: Record<string, string> = {};
  languages.forEach((language) => {
    languageAlternates[language.code] = `https://timerkit.com/${language.code}`;
  });

  return {
    title: t.homePageTitle,
    description: t.homePageDescription,
    keywords: "online time management, " + t.timeTools.toLowerCase() + ", " + t.productivitySuite.toLowerCase(),
    openGraph: {
      type: 'website',
      locale: lang === 'en' ? 'en_US' : lang,
      url: `https://timerkit.com/${lang}`,
      siteName: 'Timer Kit',
      title: t.homePageTitle,
      description: t.homePageDescription,
      images: [
        {
          url: 'https://timerkit.com/og-image.jpg',
          width: 1200,
          height: 630,
          alt: t.homePageTitle,
        }
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: t.homePageTitle,
      description: t.homePageDescription,
      images: ['https://timerkit.com/og-image.jpg'],
    },
    alternates: {
      canonical: `https://timerkit.com/${lang}`,
      languages: languageAlternates
    }
  }
}

export default async function LanguageLayout({ // Déclarer comme async pour utiliser await
  children,
  params: paramsInput, // Renommer le prop pour plus de clarté
}: {
  children: React.ReactNode
  params: { lang: string } // params est directement { lang: string }
}) {
  // Pour satisfaire l'exigence de Next.js "params should be awaited"
  const params = await paramsInput;
  const lang = params.lang;
  const direction = isRtlLang(lang) ? "rtl" : "ltr";
  console.log(`[LanguageLayout SERVER SIDE] Received awaited params.lang: "${params.lang}", Determined lang for <html>: "${lang}", Direction: "${direction}"`);

  // We'll use a data attribute to pass the language and direction to client components
  return (
    <div data-lang={lang} data-dir={direction} className={`lang-container ${direction}`}>
      {children}
      <Footer lang={lang} />
    </div>
  );
}
