'use client';

import Script from 'next/script';
import { usePathname } from 'next/navigation';
import { getTranslations } from '@/lib/i18n/translations';

interface StructuredDataProps {
  type: 'website' | 'webApplication' | 'breadcrumb' | 'organization';
  data?: Record<string, any>;
}

export function StructuredData({ type, data = {} }: StructuredDataProps) {
  const pathname = usePathname();

  // Extract language and page from pathname
  const pathSegments = pathname.split('/').filter(Boolean);
  const lang = pathSegments[0] || 'en';
  const page = pathSegments[1] || '';

  // Get translations for the current language
  const t = getTranslations(lang);

  // Base URL for the website with language
  const baseUrl = `https://timerkit.com/${lang}`;

  // Generate structured data based on type
  const generateStructuredData = () => {
    switch (type) {
      case 'website':
        return {
          '@context': 'https://schema.org',
          '@type': 'WebSite',
          'url': baseUrl,
          'name': t.homePageTitle || 'Timer Kit - Professional Time Management Tools',
          'description': t.homePageDescription || 'Free online time management tools: stopwatch, countdown, world clock, pomodoro and more.',
          'potentialAction': {
            '@type': 'SearchAction',
            'target': `${baseUrl}/search?q={search_term_string}`,
            'query-input': 'required name=search_term_string'
          },
          'inLanguage': lang,
          ...data
        };

      case 'webApplication':
        return {
          '@context': 'https://schema.org',
          '@type': 'WebApplication',
          'name': t.appName || 'Timer Kit',
          'applicationCategory': 'UtilityApplication',
          'operatingSystem': 'Web',
          'offers': {
            '@type': 'Offer',
            'price': '0',
            'priceCurrency': 'USD'
          },
          'url': baseUrl,
          'description': t.homePageDescription || 'Free online time management tools.',
          'inLanguage': lang,
          ...data
        };

      case 'breadcrumb':
        // Generate breadcrumb structured data
        const breadcrumbItems = [];

        // Home page - Use translated "Home" if available
        breadcrumbItems.push({
          '@type': 'ListItem',
          'position': 1,
          'name': t.home || 'Home',
          'item': baseUrl
        });

        // Current page (if not home)
        if (page) {
          // Try to get a translated name for the page based on the route
          let pageName = page.charAt(0).toUpperCase() + page.slice(1).replace(/-/g, ' ');

          // Check if we have a translation for this page
          if (page === 'timer' && t.timerStopwatch) {
            pageName = t.timerStopwatch;
          } else if (page === 'countdown' && t.countdown) {
            pageName = t.countdown;
          } else if (page === 'todo' && t.todoList) {
            pageName = t.todoList;
          } else if (page === 'time-tracking' && t.timeTracking) {
            pageName = t.timeTracking;
          } else if (page === 'world-clock' && t.worldClock) {
            pageName = t.worldClock;
          } else if (page === 'intervals' && t.intervals) {
            pageName = t.intervals;
          } else if (page === 'pomodoro' && t.pomodoro) {
            pageName = t.pomodoro;
          }

          breadcrumbItems.push({
            '@type': 'ListItem',
            'position': 2,
            'name': pageName,
            'item': `${baseUrl}/${page}`
          });
        }

        return {
          '@context': 'https://schema.org',
          '@type': 'BreadcrumbList',
          'itemListElement': breadcrumbItems,
          'inLanguage': lang,
          ...data
        };

      case 'organization':
        return {
          '@context': 'https://schema.org',
          '@type': 'Organization',
          'name': t.appName || 'Timer Kit',
          'url': baseUrl,
          'logo': `https://timerkit.com/logo.png`, // Logo URL should be absolute and not language-specific
          'description': t.homePageDescription || 'Free online time management tools.',
          'sameAs': [
            'https://twitter.com/timerkit',
            'https://facebook.com/timerkit',
            'https://linkedin.com/company/timerkit'
          ],
          'inLanguage': lang,
          ...data
        };

      default:
        return {};
    }
  };

  const structuredData = generateStructuredData();

  return (
    <Script
      id={`structured-data-${type}`}
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}
