import { InternalSitemap } from "@/components/internal-sitemap"
import { PageBreadcrumbs } from "@/components/page-breadcrumbs"
import { getTranslations } from "@/lib/i18n/translations"
import { getTranslatedRoute } from "@/lib/i18n/route-translations"
import { Metadata } from "next"

export async function generateMetadata({ params }: { params: Promise<{ lang: string }> }): Promise<Metadata> {
  const { lang } = await params;
  const t = getTranslations(lang);

  // Use the translated route for canonical URL
  const translatedSitemapRoute = getTranslatedRoute(lang, 'sitemap');
  const sitemapUrl = `https://timerkit.com/${lang}/${translatedSitemapRoute}`;

  return {
    title: `${t.sitemap || "Plan du site"} | ${t.appName}`,
    description: `${t.sitemapDescription || "Découvrez tous nos outils de gestion du temps et de productivité. Plan du site complet avec liens directs vers tous les outils disponibles."} ${t.appName}`,
    keywords: `sitemap, ${t.timeTools.toLowerCase()}, ${t.productivityTools.toLowerCase()}, navigation`,
    openGraph: {
      title: `${t.sitemap || "Plan du site"} | ${t.appName}`,
      description: t.sitemapDescription || "Découvrez tous nos outils de gestion du temps et de productivité.",
      type: 'website',
      locale: lang === 'en' ? 'en_US' : lang,
      url: sitemapUrl,
    },
    alternates: {
      canonical: sitemapUrl,
    }
  };
}

export default async function SitemapPage({ params }: { params: Promise<{ lang: string }> }) {
  const { lang } = await params;
  const t = getTranslations(lang);

  return (
    <div className="container mx-auto py-8">
      <PageBreadcrumbs
        lang={lang}
        customItems={[
          { label: t.sitemap || "Plan du site" }
        ]}
      />

      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">{t.sitemap || "Plan du site"}</h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          {t.sitemapIntro || "Découvrez tous nos outils de gestion du temps et de productivité. Naviguez facilement vers l'outil qui correspond à vos besoins."}
        </p>
      </div>

      <InternalSitemap lang={lang} compact={false} />

      {/* Additional SEO content */}
      <div className="mt-16 prose dark:prose-invert max-w-none">
        <div className="bg-muted/30 rounded-lg p-8">
          <h2 className="text-2xl font-bold mb-4">{t.aboutOurTools || "À propos de nos outils"}</h2>
          <p className="text-muted-foreground mb-4">
            {t.toolsOverview || "Notre suite complète d'outils de gestion du temps et de productivité est conçue pour vous aider à optimiser votre temps et améliorer votre efficacité. Tous nos outils sont gratuits, fonctionnent hors ligne et sont disponibles en plusieurs langues."}
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
            <div>
              <h3 className="text-lg font-semibold mb-2">{t.timeTools}</h3>
              <p className="text-sm text-muted-foreground">
                {t.timeToolsOverview || "Des outils essentiels pour mesurer, suivre et gérer votre temps efficacement."}
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-2">{t.productivityTools}</h3>
              <p className="text-sm text-muted-foreground">
                {t.productivityToolsOverview || "Augmentez votre productivité avec nos outils de gestion des tâches et de suivi du temps."}
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-2">{t.professionalTools}</h3>
              <p className="text-sm text-muted-foreground">
                {t.professionalToolsOverview || "Des outils spécialisés pour les professionnels et les équipes."}
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-2">{t.fitnessTools}</h3>
              <p className="text-sm text-muted-foreground">
                {t.fitnessToolsOverview || "Optimisez vos entraînements avec nos outils de fitness et d'intervalles."}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
