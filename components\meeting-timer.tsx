"use client"

import { useState, useEffect, useR<PERSON>, useCallback } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { getTranslations, formatTranslation } from "@/lib/i18n/translations"
import {
  Play,
  Pause,
  RefreshCw,
  Plus,
  Users,
  Clock,
  DollarSign,
  Save,
  Download,
  Trash2,
  AlertCircle,
  CheckCircle2,
  List,
  FileText,
} from "lucide-react"
import { useSound } from "@/components/sound-provider"
import { saveToLocalStorage as saveTo<PERSON>, get<PERSON>romLocalStorage as getF<PERSON><PERSON>, removeFromLocalStorage } from "@/lib/storage"
import { ToolControls } from "@/components/tool-controls"
import { ToolFullscreenWrapper } from "@/components/tool-fullscreen-wrapper"

// --- Storage Keys ---
const STORAGE_KEYS = {
  MEETINGS_LIST: "timetools_meetings_list",
  MEETING_DATA: "timetools_meeting_",
  ACTIVE_MEETING_ID: "timetools_active_meeting_id"
}

// --- Interfaces ---
interface Meeting {
  id: string
  createdAt: string
  updatedAt: string
  title: string
  data: MeetingData
  hasEnded: boolean
}

interface MeetingListItem {
  id: string
  title: string // Peut être une chaîne vide
}

interface MeetingTimerProps {
  lang: string
}

interface Participant {
  id: string
  name: string
  cost: number
  role?: string
}

interface AgendaItem {
  id: string
  title: string
  duration: number // in minutes
  completed: boolean
}

interface MeetingData {
  title: string
  participants: Participant[]
  plannedDuration: number // in minutes
  agendaItems: AgendaItem[]
  notes: string
}

// --- LocalStorage Wrappers ---
function saveToLocalStorage<T>(key: string, data: T): void {
  saveToLS(key, data);
}

function getFromLocalStorage<T>(key: string, defaultValue: T): T {
  return getFromLS(key, defaultValue);
}

// --- Helper function for delay ---
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));


// --- Component ---
export function MeetingTimer({ lang }: MeetingTimerProps) {
  const t = getTranslations(lang)
  const [isRunning, setIsRunning] = useState(false)
  const [time, setTime] = useState(0) // Total elapsed seconds from worker
  const [currentItemTime, setCurrentItemTime] = useState(0) // Elapsed seconds for current item from worker
  const [activeTab, setActiveTab] = useState("timer")
  const [isToolFullscreen, setIsToolFullscreen] = useState(false)

  // Meeting data state
  const [meetingTitle, setMeetingTitle] = useState("")
  const [participants, setParticipants] = useState<Participant[]>([])
  const [plannedDuration, setPlannedDuration] = useState(0); // Total planned duration in minutes (calculated from agenda)
  const [agendaItems, setAgendaItems] = useState<AgendaItem[]>([])
  const [notes, setNotes] = useState("")

  // UI State for adding items
  const [newParticipantName, setNewParticipantName] = useState("")
  const [newParticipantRole, setNewParticipantRole] = useState("")
  const [newParticipantCost, setNewParticipantCost] = useState(50)
  const [newAgendaTitle, setNewAgendaTitle] = useState("")
  const [newAgendaDuration, setNewAgendaDuration] = useState(10)

  // Other state
  const [currentAgendaItemIndex, setCurrentAgendaItemIndex] = useState(0) // Index from worker
  const [savedMeetings, setSavedMeetings] = useState<MeetingListItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showAlert, setShowAlert] = useState(false) // Controlled by worker
  const [activeMeetingId, setActiveMeetingId] = useState<string | null>(null)

  // --- Refs ---
  const workerRef = useRef<Worker | null>(null); // Ref pour le worker
  const { playSound } = useSound()

  // --- Effects ---

  // Initialize Worker
  useEffect(() => {
    // Créer le worker une seule fois au montage
    workerRef.current = new Worker('/workers/stopwatch-worker.js'); // Assurez-vous que le chemin est correct
    console.log("Worker initialized.");

    // Gérer les messages reçus du worker
    workerRef.current.onmessage = (event: MessageEvent) => {
      const { type, ...data } = event.data;
      // console.log("Message from worker:", type, data); // Debug

      switch (type) {
        case 'tick':
          setTime(data.totalTime);
          setCurrentItemTime(data.currentItemTime);
          // Mettre à jour l'index seulement s'il change VRAIMENT
          if (data.currentAgendaIndex !== currentAgendaItemIndex) {
             setCurrentAgendaItemIndex(data.currentAgendaIndex);
          }
          // Marquer l'item comme complété dans l'état React si le worker le signale (via currentItemCompleted)
          // Le worker envoie currentItemCompleted = true seulement au moment où l'item se termine.
          if (data.currentItemCompleted) {
              // L'index dans le message 'tick' est déjà l'index *suivant* si on avance automatiquement,
              // ou l'index mis à jour si on force.
              // Pour marquer le bon item, on prend l'index *avant* la mise à jour potentielle.
              const completedIndex = data.currentAgendaIndex > currentAgendaItemIndex
                  ? data.currentAgendaIndex - 1 // Si l'index a augmenté (auto ou forcé)
                  : currentAgendaItemIndex; // Si l'index n'a pas changé (ex: forcé sur le dernier item, ou autre cas)

              // Assurer que l'index est valide avant de marquer
              if (completedIndex >= 0 && completedIndex < agendaItems.length) {
                  setAgendaItems(prev =>
                      prev.map((item, index) =>
                          index === completedIndex ? { ...item, completed: true } : item
                      )
                  );
              }
          }
          if (data.playSound) {
            playSound(data.playSound as "bell"); // Jouer le son demandé par le worker
          }
          // Mettre à jour l'état de l'alerte
          if (data.showAlert !== showAlert) {
              setShowAlert(data.showAlert);
          }
          break;
        case 'paused':
           // Mettre à jour l'état une dernière fois à la pause
           setTime(data.totalTime);
           setCurrentItemTime(data.currentItemTime);
           setCurrentAgendaItemIndex(data.currentAgendaIndex);
           setShowAlert(data.showAlert);
           setIsRunning(false); // Assurer que l'état isRunning est correct
           break;
        case 'reset':
          // Mettre à jour l'état après un reset du worker
          setTime(0);
          setCurrentItemTime(0);
          setCurrentAgendaItemIndex(0);
          setShowAlert(false);
          setIsRunning(false);
          // Réinitialiser l'état 'completed' de l'agenda localement aussi lors d'un reset worker
          setAgendaItems(prev => prev.map(item => ({ ...item, completed: false })));
          break;
        default:
            console.warn("Unknown message type received from worker:", type);
      }
    };

    // Nettoyer le worker lors du démontage du composant
    return () => {
      console.log("Terminating worker...");
      workerRef.current?.terminate();
      workerRef.current = null;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [playSound, agendaItems.length]); // Added agendaItems.length dependency for completion logic

  // Initial Load from localStorage
  useEffect(() => {
    console.log("Initial load effect running...");
    setIsLoading(true);
    const meetingsList = getFromLocalStorage<MeetingListItem[]>(STORAGE_KEYS.MEETINGS_LIST, []);
    setSavedMeetings(meetingsList);
    const storedActiveMeetingId = getFromLocalStorage<string | null>(STORAGE_KEYS.ACTIVE_MEETING_ID, null);

    let loadedSuccessfully = false;
    let loadedMeetingData: MeetingData | null = null;

    if (storedActiveMeetingId) {
      const meeting = getFromLocalStorage<Meeting | null>(`${STORAGE_KEYS.MEETING_DATA}${storedActiveMeetingId}`, null);
      if (meeting && meeting.data) {
        loadedMeetingData = meeting.data; // Stocker les données chargées
        setMeetingTitle(loadedMeetingData.title || "");
        setParticipants(loadedMeetingData.participants ? loadedMeetingData.participants.map(p => ({ ...p })) : []);
        // Mettre à jour l'état de l'agenda AVANT d'envoyer au worker
        const loadedAgenda = loadedMeetingData.agendaItems ? loadedMeetingData.agendaItems.map(item => ({ ...item })) : [];
        setAgendaItems(loadedAgenda);
        setNotes(loadedMeetingData.notes || "");
        setActiveMeetingId(storedActiveMeetingId);
        // Ne pas appeler resetTimer ici, le worker sera initialisé plus tard
        loadedSuccessfully = true;
      } else {
        removeFromLocalStorage(STORAGE_KEYS.ACTIVE_MEETING_ID);
      }
    }

    if (!loadedSuccessfully) {
        clearMeetingState(false); // Ne pas envoyer de message au worker ici
        setActiveMeetingId(null);
    }

    // Envoyer les données initiales au worker APRÈS que l'état React soit mis à jour
    // et que le worker soit prêt (peut nécessiter un petit délai ou une vérification)
    const initialDataTimer = setTimeout(() => {
        if (workerRef.current) {
            console.log("Sending initial data to worker after load:", loadedMeetingData);
            const initialAgenda = loadedMeetingData?.agendaItems || [];
            const initialPlannedDuration = initialAgenda.reduce((sum, item) => sum + item.duration, 0);
            workerRef.current.postMessage({
                command: 'updateData',
                payload: {
                    agendaItems: initialAgenda.map(item => ({...item})), // Send a copy
                    plannedDuration: initialPlannedDuration,
                }
            });
            // Envoyer un état initial 'reset' pour s'assurer que le worker est à 0
            workerRef.current.postMessage({ command: 'reset' });
        }
        setIsLoading(false);
        console.log("Initial load complete, isLoading set to false.");
    }, 100); // Petit délai pour s'assurer que le worker est initialisé

    return () => clearTimeout(initialDataTimer);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Exécuter une seule fois

  // Calculate planned duration whenever agenda items change
  useEffect(() => {
    const totalDuration = agendaItems.reduce((sum, item) => sum + item.duration, 0);
    setPlannedDuration(totalDuration); // Update local state for display

    // Envoyer la mise à jour au worker si l'agenda change (et pas pendant le chargement)
    if (workerRef.current && !isLoading) {
        console.log("Sending agenda update to worker");
        workerRef.current.postMessage({
            command: 'updateData',
            payload: {
                agendaItems: agendaItems.map(item => ({...item})), // Send a copy
                plannedDuration: totalDuration
            }
        });
    }
  }, [agendaItems, isLoading]);

  // Auto-save current meeting state
  useEffect(() => {
    if (isLoading || !activeMeetingId) return;
    console.log(`Auto-saving data for active meeting ID: ${activeMeetingId}`);
    saveMeetingInternal(activeMeetingId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [meetingTitle, participants, agendaItems, notes, activeMeetingId, isLoading]); // plannedDuration removed as it's derived from agendaItems

  // --- Core Functions ---

  const toggleTimer = useCallback(() => {
    const nextIsRunning = !isRunning;
    if (workerRef.current) {
      if (nextIsRunning) {
        // Si on démarre de 0, envoyer l'agenda actuel et demander une réinitialisation de l'agenda dans le worker
        const shouldResetAgendaInWorker = time === 0;
        console.log("Sending START to worker", { shouldResetAgendaInWorker });
        // Calculate current planned duration based on current agendaItems state
        const currentPlannedDuration = agendaItems.reduce((sum, item) => sum + item.duration, 0);
        workerRef.current.postMessage({
          command: 'start',
          payload: {
              resetAgenda: shouldResetAgendaInWorker,
              // Envoyer les données actuelles au cas où elles auraient changé
              agendaItems: agendaItems.map(item => ({...item})), // Send a copy
              plannedDuration: currentPlannedDuration
          }
        });
        // Si on démarre de 0 dans le composant, réinitialiser l'état 'completed' localement aussi
        if (shouldResetAgendaInWorker) {
            setAgendaItems(prev => prev.map(item => ({ ...item, completed: false })));
            // Les autres états (index, time, alert) seront mis à jour par le worker via 'reset' puis 'tick'
        }
      } else {
        console.log("Sending PAUSE to worker");
        workerRef.current.postMessage({ command: 'pause' });
      }
      // Mettre à jour l'état local immédiatement pour la réactivité de l'UI
      // Le worker enverra un message 'paused' pour confirmer/corriger l'état final
      setIsRunning(nextIsRunning);
    }
  }, [isRunning, workerRef, time, agendaItems]); // plannedDuration removed, calculated inside

  const resetTimer = useCallback(() => {
    if (workerRef.current) {
      console.log("Sending RESET to worker");
      workerRef.current.postMessage({ command: 'reset' });
      // L'état local (isRunning, time, currentItemTime, currentAgendaIndex, showAlert)
      // sera mis à jour par le message 'reset' du worker.
      // L'état 'completed' des items est aussi géré dans le handler du message 'reset'.
    }
  }, [workerRef]);

  // clearMeetingState (Modifié pour utiliser resetTimer)
  const clearMeetingState = useCallback((sendMessageToWorker = true) => {
      console.log("Clearing meeting state...");
      setMeetingTitle("");
      setParticipants([]);
      setAgendaItems([]); // Déclenchera l'effet pour mettre à jour le worker avec une liste vide
      setNotes("");
      setActiveMeetingId(null); // Clear active ID in component state ONLY
      removeFromLocalStorage(STORAGE_KEYS.ACTIVE_MEETING_ID); // Also remove from storage
      setNewParticipantName("");
      setNewParticipantRole("");
      setNewParticipantCost(50);
      setNewAgendaTitle("");
      setNewAgendaDuration(10);

      if (sendMessageToWorker) {
          resetTimer(); // Envoie la commande reset au worker et reset l'état completed local via le handler
      } else {
          // Si on ne veut pas envoyer de message (ex: pendant le chargement initial avant que le worker soit prêt)
          // on réinitialise juste l'état local du timer manuellement
          setIsRunning(false);
          setTime(0);
          setCurrentItemTime(0);
          setCurrentAgendaItemIndex(0);
          setShowAlert(false);
      }
      console.log("Meeting state cleared.");
  }, [resetTimer]); // Dépend de resetTimer

  // saveMeetingInternal (Logic for list update remains the same as previous version)
  // useCallback pour la stabilité si utilisé dans useEffect
  const saveMeetingInternal = useCallback((idToSave: string | null): Meeting | null => {
    if (!idToSave) {
        console.warn("saveMeetingInternal called with no ID to save.");
        return null;
    }

    const existingMeeting = getFromLocalStorage<Meeting | null>(`${STORAGE_KEYS.MEETING_DATA}${idToSave}`, null);

    // Skip data save only if title is empty AND it's an UPDATE
    if (meetingTitle.trim() === "" && existingMeeting) {
        console.warn(`Meeting title is empty for existing meeting ID ${idToSave}, data update skipped.`);
        // Still proceed to update the list if necessary (e.g., title was cleared)
    } else {
      // console.log(`Saving meeting data for ID: ${idToSave}. Existing meeting found: ${!!existingMeeting}`);
    }

    const now = new Date().toISOString();
    let meetingToSave: Meeting;

    // Calculate planned duration from current agendaItems state
    const currentPlannedDuration = agendaItems.reduce((sum, item) => sum + item.duration, 0);

    const currentDataPayload: MeetingData = {
        title: meetingTitle, // Use current state title
        participants: participants.map(p => ({ ...p })),
        plannedDuration: currentPlannedDuration, // Use calculated value
        agendaItems: agendaItems.map(item => ({ ...item })),
        notes
    };

    if (existingMeeting) {
        // Update existing meeting data
        meetingToSave = {
            ...existingMeeting,
            title: meetingTitle, // Update top-level title
            updatedAt: now,
            data: currentDataPayload,
            // Keep hasEnded status from existing meeting unless explicitly changed
        };
    } else {
        // Create new meeting structure
        meetingToSave = {
            id: idToSave,
            createdAt: now,
            updatedAt: now,
            title: meetingTitle, // Initially empty when called from startNewMeeting
            data: currentDataPayload,
            hasEnded: false // New meetings haven't ended
        };
    }

    // Save the full meeting data regardless of title for consistency (unless skipped above)
    if (!(meetingTitle.trim() === "" && existingMeeting)) {
        saveToLocalStorage(`${STORAGE_KEYS.MEETING_DATA}${idToSave}`, meetingToSave);
    } else {
        // If data save was skipped, ensure meetingToSave still holds the existing data for return value
        if (existingMeeting) meetingToSave = existingMeeting;
    }


    // --- LIST UPDATE LOGIC ---
    const meetingsList = getFromLocalStorage<MeetingListItem[]>(STORAGE_KEYS.MEETINGS_LIST, []);
    const existingIndex = meetingsList.findIndex(m => m.id === idToSave);
    let listNeedsUpdate = false;
    let updatedList: MeetingListItem[];

    const newTitleFromState = meetingTitle.trim(); // Get current title from state

    if (existingIndex > -1) {
        // UPDATE existing meeting in the list
        const currentListTitle = meetingsList[existingIndex].title;

        // Update if the title in state is different from the title in the list
        if (currentListTitle !== newTitleFromState) {
            updatedList = meetingsList.map(m => m.id === idToSave ? { ...m, title: newTitleFromState } : m);
            listNeedsUpdate = true;
        } else {
             updatedList = meetingsList; // No change needed
        }

    } else {
        // ADD new meeting to the list
        // Add with the current title (might be empty initially)
        updatedList = [...meetingsList, { id: idToSave, title: newTitleFromState }];
        listNeedsUpdate = true;
    }

    if (listNeedsUpdate) {
        saveToLocalStorage(STORAGE_KEYS.MEETINGS_LIST, updatedList);
        setSavedMeetings(updatedList); // Update UI
    }
    // --- END LIST UPDATE LOGIC ---

    // Ensure we return a valid Meeting object or null
    return meetingToSave || null;
  }, [meetingTitle, participants, agendaItems, notes]); // Dependencies for data payload and list update

  // startNewMeeting (Utilise clearMeetingState)
  const startNewMeeting = useCallback(async () => {
    console.log("Starting new meeting process...");
    setIsLoading(true);

    // Save the current meeting if it exists and has a title before clearing
    if (activeMeetingId && meetingTitle.trim() !== "") {
        saveMeetingInternal(activeMeetingId);
    }

    const newMeetingId = Date.now().toString();
    clearMeetingState(true); // Efface l'état et envoie 'reset' au worker

    // Attendre un court instant pour que l'état (y compris l'agenda vide) se propage au worker
    await delay(50);

    setActiveMeetingId(newMeetingId);
    saveToLocalStorage(STORAGE_KEYS.ACTIVE_MEETING_ID, newMeetingId);
    console.log(`Set new active meeting ID: ${newMeetingId}`);

    // Sauvegarder immédiatement la nouvelle réunion (vide) pour qu'elle apparaisse dans la liste
    saveMeetingInternal(newMeetingId);

    // Mettre le focus sur le titre après un court délai pour s'assurer que l'UI est prête
    requestAnimationFrame(() => {
        document.getElementById("meeting-title")?.focus();
    });

    setActiveTab("timer");
    setIsLoading(false); // Mettre isLoading à false *après* que tout soit prêt
    console.log("New meeting setup complete.");
  }, [activeMeetingId, meetingTitle, saveMeetingInternal, clearMeetingState]);

  // loadMeeting (Envoie les données au worker)
  const loadMeeting = useCallback((id: string) => {
    console.log(`Loading meeting with ID: ${id}`);
    if (id === activeMeetingId && !isLoading) return; // Avoid reloading the same meeting

    setIsLoading(true);

    // Save the current meeting before loading another one
    if (activeMeetingId && meetingTitle.trim() !== "") {
      saveMeetingInternal(activeMeetingId);
    }

    const meetingToLoad = getFromLocalStorage<Meeting | null>(`${STORAGE_KEYS.MEETING_DATA}${id}`, null);

    if (meetingToLoad && meetingToLoad.data) {
      const loadedData = meetingToLoad.data;
      setMeetingTitle(loadedData.title || "");
      setParticipants(loadedData.participants ? loadedData.participants.map(p => ({ ...p })) : []);
      const loadedAgenda = loadedData.agendaItems ? loadedData.agendaItems.map(item => ({ ...item })) : [];
      setAgendaItems(loadedAgenda); // Met à jour l'état local, déclenchera l'effet pour envoyer au worker
      setNotes(loadedData.notes || "");
      setActiveMeetingId(id);
      saveToLocalStorage(STORAGE_KEYS.ACTIVE_MEETING_ID, id);

      // Envoyer les nouvelles données et réinitialiser le worker
      if (workerRef.current) {
          console.log("Sending loaded data and reset to worker");
          const loadedPlannedDuration = loadedAgenda.reduce((sum, item) => sum + item.duration, 0);
          // Envoyer d'abord les données
          workerRef.current.postMessage({
              command: 'updateData',
              payload: {
                  agendaItems: loadedAgenda.map(item => ({...item})), // Send copy
                  plannedDuration: loadedPlannedDuration,
              }
          });
          // Puis envoyer reset pour s'assurer que le timer est à 0 et l'état completed est reset
          workerRef.current.postMessage({ command: 'reset' });
      } else {
          // Si le worker n'est pas prêt, réinitialiser l'état local manuellement
          resetTimer(); // Appelle la fonction qui essaiera d'envoyer le message
      }

      setActiveTab("timer");
    } else {
      console.error(`Failed to load meeting data for ID: ${id}. Removing invalid reference.`);
      alert(formatTranslation(t.errorLoadFailed, { id: id }));
      // Supprimer la référence invalide de la liste et de l'ID actif
      const updatedMeetingsList = savedMeetings.filter(m => m.id !== id);
      saveToLocalStorage(STORAGE_KEYS.MEETINGS_LIST, updatedMeetingsList);
      setSavedMeetings(updatedMeetingsList);
      if (activeMeetingId === id) {
          removeFromLocalStorage(STORAGE_KEYS.ACTIVE_MEETING_ID);
          clearMeetingState(true); // Clear state if the failed load was the active one
      }
    }

    // Utiliser un petit délai pour laisser le temps aux mises à jour d'état de se propager
    setTimeout(() => {
        setIsLoading(false);
        console.log("Finished loading meeting process.");
    }, 50);
  }, [activeMeetingId, isLoading, meetingTitle, saveMeetingInternal, t, resetTimer, clearMeetingState, savedMeetings]); // Added dependencies

  // deleteSavedMeeting (Utilise clearMeetingState si nécessaire)
  const deleteSavedMeeting = useCallback((id: string) => {
    const meetingToDelete = savedMeetings.find(m => m.id === id);
    const titleForConfirm = meetingToDelete?.title || `(${t.noTitle || 'Sans titre'})`;
    const confirmationMessage = formatTranslation(t.confirmDeleteMeeting, { title: titleForConfirm });

    if (!confirm(confirmationMessage)) return;

    setIsLoading(true);
    removeFromLocalStorage(`${STORAGE_KEYS.MEETING_DATA}${id}`);
    const updatedMeetingsList = savedMeetings.filter(m => m.id !== id);
    saveToLocalStorage(STORAGE_KEYS.MEETINGS_LIST, updatedMeetingsList);
    setSavedMeetings(updatedMeetingsList);

    // Si la réunion supprimée était la réunion active, effacer l'état
    if (activeMeetingId === id) {
      console.log("Deleting active meeting. Clearing state.");
      clearMeetingState(true); // Efface l'état et envoie 'reset' au worker
    }

    setTimeout(() => setIsLoading(false), 50);
  }, [savedMeetings, activeMeetingId, t, clearMeetingState]); // Added dependencies

  // --- Helper Functions ---

  const addParticipant = useCallback(() => {
    if (newParticipantName.trim() === "") return;
    const participant: Participant = {
      id: Date.now().toString(),
      name: newParticipantName,
      cost: newParticipantCost,
      role: newParticipantRole.trim() || undefined,
    };
    setParticipants(prev => [...prev, participant]);
    setNewParticipantName("");
    setNewParticipantRole("");
    setNewParticipantCost(50);
  }, [newParticipantName, newParticipantCost, newParticipantRole]);

  const removeParticipant = useCallback((id: string) => {
    setParticipants(prev => prev.filter((p) => p.id !== id));
  }, []);

  const addAgendaItem = useCallback(() => {
    if (newAgendaTitle.trim() === "") return;
    const agendaItem: AgendaItem = {
      id: Date.now().toString(),
      title: newAgendaTitle,
      duration: newAgendaDuration,
      completed: false,
    };
    setAgendaItems(prev => [...prev, agendaItem]); // This will trigger the useEffect to update the worker
    setNewAgendaTitle("");
    setNewAgendaDuration(10);
  }, [newAgendaTitle, newAgendaDuration]);

  // removeAgendaItem: Filtre la liste locale. L'effet [agendaItems] enverra la mise à jour au worker.
  const removeAgendaItem = useCallback((id: string) => {
    setAgendaItems(prev => prev.filter((item) => item.id !== id));
    // L'effet [agendaItems] enverra la mise à jour au worker.
    // Le worker gérera la logique si l'item courant est supprimé.
  }, []);

  // markAgendaItemComplete: Marque/Démarque localement.
  // Le worker gère sa propre logique de complétion automatique.
  // Le marquage manuel ici est principalement pour l'UI et la sauvegarde.
  const markAgendaItemComplete = useCallback((id: string) => {
    setAgendaItems(prev =>
      prev.map((item) => (item.id === id ? { ...item, completed: !item.completed } : item))
    );
    // Optionnel: On pourrait envoyer une commande 'updateItemStatus' au worker
    // si son état 'completed' interne doit être synchronisé avec le marquage manuel.
    // Pour l'instant, on suppose que seul le worker marque comme complété via le temps.
  }, []);

  // moveToNextAgendaItem: Envoie la commande au worker.
  const moveToNextAgendaItem = useCallback(() => {
    // On ne peut passer manuellement que si le timer tourne et qu'on n'est pas au dernier item
    if (!isRunning || currentAgendaItemIndex >= agendaItems.length - 1) return;

    if (workerRef.current) {
        console.log("Sending FORCE_NEXT_ITEM to worker");
        // Envoyer la commande au worker pour qu'il gère le changement d'état
        workerRef.current.postMessage({ command: 'forceNextItem' });

        // Jouer le son immédiatement pour un retour rapide.
        // Le worker enverra aussi une demande, mais elle pourrait arriver légèrement après.
        // Si le son joue deux fois, on pourrait supprimer cette ligne et se fier uniquement au worker.
        playSound("bell");

        // --- Suppression de la simulation locale ---
        // L'état React (index, temps item, complétion) sera mis à jour
        // par le message 'tick' envoyé par le worker après avoir traité 'forceNextItem'.
    }
  }, [isRunning, currentAgendaItemIndex, agendaItems.length, playSound, workerRef]); // Ajout de workerRef aux dépendances

  const calculateCost = useCallback((elapsedSeconds: number = time): number => {
    const hours = elapsedSeconds / 3600;
    return participants.reduce((total, participant) => {
      const costPerHour = typeof participant.cost === 'number' ? participant.cost : 0;
      return total + costPerHour * hours;
    }, 0);
  }, [participants, time]); // Depends on participants and the current time state


  const exportMeetingData = useCallback(() => {
    if (!activeMeetingId) {
        alert(t.noActiveMeetingToExport);
        return;
    }
     // Utiliser l'état actuel pour l'exportation, car il est auto-sauvegardé
     const currentDataToExport: MeetingData = {
        title: meetingTitle,
        participants: participants,
        plannedDuration: plannedDuration, // Utiliser l'état calculé
        agendaItems: agendaItems,
        notes: notes
     };

    // Utiliser le temps total actuel du worker pour la durée réelle
    const actualDurationSeconds = time;

    const exportData = {
      title: currentDataToExport.title,
      exportedAt: new Date().toISOString(),
      plannedDurationMinutes: currentDataToExport.plannedDuration,
      actualDurationSeconds: actualDurationSeconds, // Use accurate time from worker state
      finalCost: calculateCost(actualDurationSeconds), // Pass accurate time to cost calculation
      participants: currentDataToExport.participants,
      agendaItems: currentDataToExport.agendaItems.map((item) => ({
        title: item.title,
        plannedDurationMinutes: item.duration,
        // Utiliser l'état 'completed' actuel du composant pour le statut
        status: item.completed ? "Completed" : "Not completed",
      })),
      notes: currentDataToExport.notes,
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;
    const exportFileDefaultName = `meeting-${(currentDataToExport.title || t.noTitle || 'untitled').replace(/\s+/g, "-").toLowerCase()}-${new Date().toISOString().split("T")[0]}.json`;

    const linkElement = document.createElement("a");
    linkElement.setAttribute("href", dataUri);
    linkElement.setAttribute("download", exportFileDefaultName);
    linkElement.click();
    linkElement.remove();
  }, [activeMeetingId, t, time, calculateCost, meetingTitle, participants, plannedDuration, agendaItems, notes]); // Added all relevant state dependencies

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  const getProgressPercentage = (): number => {
    if (plannedDuration <= 0) return 0;
    const plannedDurationInSeconds = plannedDuration * 60;
    if (plannedDurationInSeconds <= 0) return 0;
    // Use the accurate 'time' state from worker
    return Math.min((time / plannedDurationInSeconds) * 100, 100);
  };

  const getCurrentAgendaItem = (): AgendaItem | undefined => {
    // Utiliser l'état local `currentAgendaItemIndex` qui est synchronisé depuis le worker
    if (currentAgendaItemIndex >= 0 && currentAgendaItemIndex < agendaItems.length) {
        return agendaItems[currentAgendaItemIndex];
    }
    return undefined;
  };

  const getAgendaItemProgress = (): number => {
    const currentItem = getCurrentAgendaItem(); // Utilise l'index synchronisé
    if (!currentItem || currentItem.duration <= 0) return 0;
    const itemDurationSeconds = currentItem.duration * 60;
    if (itemDurationSeconds <= 0) return 0;
    // Utiliser l'état local `currentItemTime` synchronisé depuis le worker
    // S'assurer que le temps affiché ne dépasse pas la durée prévue de l'item
    const clampedItemTime = Math.min(currentItemTime, itemDurationSeconds);
    return Math.min((clampedItemTime / itemDurationSeconds) * 100, 100);
  };

  const [showParticipantForm, setShowParticipantForm] = useState(false);
  const [showAgendaItemForm, setShowAgendaItemForm] = useState(false);
  const [isLoadDialogOpen, setIsLoadDialogOpen] = useState(false);

  // Pour la mise à jour du titre de l'onglet
  const originalTitle = useRef<string>("");
  const [refreshTitleToggle, setRefreshTitleToggle] = useState(false);

  const timerUpdateIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Sauvegarder le titre original de la page au chargement du composant
  useEffect(() => {
    originalTitle.current = document.title;
    return () => {
      document.title = originalTitle.current;
    };
  }, []);

  // Mettre à jour le titre de l'onglet avec le temps écoulé de la réunion
  useEffect(() => {
    if (isRunning) {
      // Mettre à jour le titre initialement
      updateTabTitle();

      // Mettre à jour le titre toutes les secondes
      if (!timerUpdateIntervalRef.current) {
        timerUpdateIntervalRef.current = setInterval(() => {
          setRefreshTitleToggle(prev => !prev);
        }, 1000);
      }
    } else {
      // Restaurer le titre original si le timer est arrêté
      document.title = originalTitle.current;

      // Arrêter les mises à jour du titre
      if (timerUpdateIntervalRef.current) {
        clearInterval(timerUpdateIntervalRef.current);
        timerUpdateIntervalRef.current = null;
      }
    }

    return () => {
      if (timerUpdateIntervalRef.current) {
        clearInterval(timerUpdateIntervalRef.current);
        timerUpdateIntervalRef.current = null;
      }
    };
  }, [isRunning, refreshTitleToggle]);

  // Fonction pour mettre à jour le titre de l'onglet
  const updateTabTitle = () => {
    const formattedTime = formatTime(time);
    const titlePrefix = meetingTitle.trim() || t.meeting || "Réunion";
    document.title = `${formattedTime} - ${titlePrefix}`;
  };

  // Commencer le compteur de temps
  const startTimer = () => {
    setIsRunning(true);
    if (workerRef.current) {
      workerRef.current.postMessage({ command: 'start' });
    }
  };

  // Arrêter le compteur de temps
  const stopTimer = () => {
    setIsRunning(false);
    if (workerRef.current) {
      workerRef.current.postMessage({ command: 'pause' });
    }

    // Restaurer le titre original
    document.title = originalTitle.current;

    // Arrêter les mises à jour du titre
    if (timerUpdateIntervalRef.current) {
      clearInterval(timerUpdateIntervalRef.current);
      timerUpdateIntervalRef.current = null;
    }
  };

  // --- Render ---
  // Le JSX reste globalement le même, car il lit les états (time, currentItemTime, etc.)
  // qui sont maintenant mis à jour par le worker via le handler onmessage.
  const meetingContent = (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-2 w-full mb-6 h-auto">
        <TabsTrigger
          value="timer"
          className="flex items-center justify-center gap-2 text-xs sm:text-sm md:text-base"
        >
          <Clock className="h-4 w-4" />
          {t.timer}
        </TabsTrigger>
        <TabsTrigger
          value="participants"
          className="flex items-center justify-center gap-2 text-xs sm:text-sm md:text-base"
        >
          <Users className="h-4 w-4" />
          {t.participants} <span className="text-xs ml-1">({participants.length})</span>
        </TabsTrigger>
        <TabsTrigger
          value="agenda"
          className="flex items-center justify-center gap-2 text-xs sm:text-sm md:text-base"
        >
          <List className="h-4 w-4" />
          {t.agenda} <span className="text-xs ml-1">({agendaItems.length})</span>
        </TabsTrigger>
        <TabsTrigger
          value="notes"
          className="flex items-center justify-center gap-2 text-xs sm:text-sm md:text-base"
        >
          <FileText className="h-4 w-4" />
          {t.notes}
        </TabsTrigger>
      </TabsList>

      {/* === Timer Tab === */}
      <TabsContent value="timer" className="space-y-6">
        {/* Meeting Title Input */}
        <div className="mb-6">
          <Label htmlFor="meeting-title" className="mb-2 block">
            {t.meetingTitle} <span className="text-red-500">*</span>
          </Label>
          <Input
            id="meeting-title"
            value={meetingTitle}
            onChange={(e) => setMeetingTitle(e.target.value)}
            placeholder={t.enterMeetingTitle}
            className="text-lg font-medium"
            disabled={isLoading}
          />
        </div>

        {/* Planned Duration & Cost */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <Label className="mb-2 block">{t.plannedDuration}</Label>
             <div className="text-2xl font-bold text-primary">
                {Math.floor(plannedDuration / 60)}
                <span className="text-lg font-normal">h</span>
                {' '}
                {plannedDuration % 60}
                <span className="text-lg font-normal">m</span>
              </div>
            <div className="text-sm text-muted-foreground mt-1">
              ({formatTime(plannedDuration * 60)})
            </div>
          </div>

          <div>
            <Label className="mb-2 block">{t.estimatedCost}</Label>
            <div className="text-2xl font-bold text-primary flex items-center">
              <DollarSign className="h-6 w-6 mr-1" />
              {new Intl.NumberFormat(lang, {
                style: "currency",
                currency: "USD", // Consider making currency configurable
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              }).format(calculateCost())} {/* Uses current 'time' state from worker */}
            </div>
             <p className="text-xs text-muted-foreground mt-1">{t.basedOnElapsedTime}</p>
          </div>
        </div>

        {/* Timer Display & Progress */}
        <div className="text-center py-8 border rounded-lg mb-6 relative">
          {/* Alert (utilise l'état 'showAlert' mis à jour par le worker) */}
          <AnimatePresence>
            {showAlert && plannedDuration > 0 && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0 }}
                className="absolute top-2 right-2 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-3 py-1 rounded-full text-sm flex items-center gap-1 z-10"
              >
                <AlertCircle className="h-4 w-4" />
                {t.approachingTimeLimit}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Time (utilise l'état 'time' mis à jour par le worker) */}
          <motion.div
            className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-mono font-bold mb-6"
            key={time} // Key ensures animation on change
            initial={{ scale: 0.98, opacity: 0.8 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.15 }}
          >
            {formatTime(time)}
          </motion.div>

          {/* Overall Progress Bar (utilise 'time' et 'plannedDuration') */}
          {plannedDuration > 0 && (
            <div className="w-full max-w-md mx-auto mb-6 px-4">
              <Progress value={getProgressPercentage()} className="h-3" />
              <div className="flex justify-between text-xs text-muted-foreground mt-1">
                <span>0:00:00</span>
                <span>{formatTime(plannedDuration * 60)}</span>
              </div>
            </div>
          )}

          {/* Current Agenda Item Display (utilise getCurrentAgendaItem, getAgendaItemProgress, formatTime(currentItemTime)) */}
          {isRunning && agendaItems.length > 0 && getCurrentAgendaItem() && (
            <div className="mt-6 mb-4 px-4">
              <div className="text-sm text-muted-foreground mb-1">
                {t.currentAgendaItem} ({currentAgendaItemIndex + 1}/{agendaItems.length}):
              </div>
              <div className="font-medium mb-2 truncate" title={getCurrentAgendaItem()?.title}>{getCurrentAgendaItem()?.title}</div>
              <div className="w-full max-w-xs mx-auto">
                <Progress value={getAgendaItemProgress()} className="h-2" />
                 <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    {/* Display accurate current item time */}
                    <span>{formatTime(currentItemTime)}</span>
                    <span>{formatTime((getCurrentAgendaItem()?.duration || 0) * 60)}</span>
                </div>
              </div>
              {/* Manual Next Item Button (appelle moveToNextAgendaItem) */}
              <div className="mt-3 flex justify-center">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={moveToNextAgendaItem}
                  disabled={currentAgendaItemIndex >= agendaItems.length - 1 || !isRunning} // Désactiver si pas en cours ou dernier item
                  title={t.moveToNextItem}
                >
                  {t.nextItem} &raquo;
                </Button>
              </div>
            </div>
          )}

          {/* Timer Controls (appellent toggleTimer, resetTimer) */}
          <div className="flex justify-center gap-6 mt-8">
            <Button
              onClick={toggleTimer}
              size="lg"
              className="w-20 h-20 rounded-full"
              variant={isRunning ? "destructive" : "default"}
              disabled={isLoading}
              aria-label={isRunning ? t.pause : t.start}
            >
              {isRunning ? <Pause className="h-8 w-8" /> : <Play className="h-8 w-8" />}
            </Button>
            <Button
              onClick={resetTimer}
              size="lg"
              variant="outline"
              className="w-18 h-18 rounded-full"
              disabled={(time === 0 && !isRunning) || isLoading}
              aria-label={t.reset}
            >
              <RefreshCw className="h-7 w-7" />
            </Button>
          </div>
        </div>

        {/* Load Saved Meetings (appelle loadMeeting) */}
        {savedMeetings.length > 0 && (
          <div className="border-t pt-4">
            <Label className="mb-2 block">{t.loadSavedMeeting}</Label>
            <div className="flex flex-wrap gap-2">
              {savedMeetings.map((meeting) => (
                <div key={meeting.id} className="flex items-center gap-1 group">
                  <Button
                    variant={activeMeetingId === meeting.id ? "secondary" : "outline"}
                    size="sm"
                    onClick={() => loadMeeting(meeting.id)}
                    disabled={isLoading}
                    title={meeting.title || `(${t.noTitle})`}
                    className="max-w-[150px] truncate"
                  >
                    {meeting.title || `(${t.noTitle})`}
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => deleteSavedMeeting(meeting.id)}
                    className="h-8 w-8 text-destructive opacity-0 group-hover:opacity-100 transition-opacity"
                    disabled={isLoading}
                    aria-label={`${t.delete} ${meeting.title || `(${t.noTitle})`}`}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}
      </TabsContent>

      {/* === Participants Tab === */}
      <TabsContent value="participants">
        <div className="space-y-6">
          <div>
            <h3 className="font-medium mb-4 flex justify-between items-center">
              <span>{t.participants} ({participants.length})</span>
            </h3>

            {/* Participants List */}
            <div className="space-y-3 mb-6 max-h-60 overflow-y-auto pr-2">
              {participants.length === 0 && (
                  <p className="text-sm text-muted-foreground">{t.noParticipantsAdded}</p>
              )}
              {participants.map((participant) => (
                <div key={participant.id} className="flex justify-between items-center p-3 border rounded-md hover:bg-muted/50">
                  <div className="flex-1 overflow-hidden mr-2">
                    <p className="font-medium truncate" title={participant.name}>{participant.name}</p>
                    <div className="flex items-center gap-2 flex-wrap">
                      {participant.role && (
                        <Badge variant="secondary" className="text-xs whitespace-nowrap">
                          {participant.role}
                        </Badge>
                      )}
                      <p className="text-sm text-muted-foreground whitespace-nowrap">
                        {new Intl.NumberFormat(lang, { style: "currency", currency: "USD" }).format(
                          participant.cost || 0,
                        )}
                        /h
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeParticipant(participant.id)}
                    className="h-8 text-destructive flex-shrink-0"
                    aria-label={`${t.remove} ${participant.name}`}
                    disabled={isLoading}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>

            {/* Add Participant Form */}
            <div className="border-t pt-4">
                <h4 className="font-medium mb-3">{t.addParticipant}</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <Label htmlFor="participant-name">{t.name} <span className="text-red-500">*</span></Label>
                    <Input
                    id="participant-name"
                    placeholder={t.participantName}
                    value={newParticipantName}
                    onChange={(e) => setNewParticipantName(e.target.value)}
                    disabled={isLoading}
                    />
                </div>
                <div>
                    <Label htmlFor="participant-role">{t.role} ({t.optional})</Label>
                    <Input
                    id="participant-role"
                    placeholder={t.role}
                    value={newParticipantRole}
                    onChange={(e) => setNewParticipantRole(e.target.value)}
                    disabled={isLoading}
                    />
                </div>
                <div>
                    <Label htmlFor="participant-cost">{t.hourlyRate} ($)</Label>
                    <Input
                    id="participant-cost"
                    type="number"
                    min="0"
                    step="1"
                    value={newParticipantCost}
                    onChange={(e) => setNewParticipantCost(Number(e.target.value) || 0)}
                    disabled={isLoading}
                    />
                </div>
                </div>

                <Button onClick={addParticipant} disabled={newParticipantName.trim() === "" || isLoading}>
                <Plus className="h-4 w-4 mr-2" />
                {t.addParticipant}
                </Button>
            </div>
          </div>
        </div>
      </TabsContent>

      {/* === Agenda Tab === */}
      <TabsContent value="agenda">
        <div className="space-y-6">
          <div>
            <h3 className="font-medium mb-4 flex justify-between items-center">
                <span>{t.agenda} ({agendaItems.length})</span>
            </h3>

            {/* Agenda List */}
            <div className="space-y-3 mb-6 max-h-60 overflow-y-auto pr-2">
               {agendaItems.length === 0 && (
                  <p className="text-sm text-muted-foreground">{t.noAgendaItemsAdded}</p>
              )}
              {agendaItems.map((item, index) => (
                <div
                  key={item.id}
                  className={`flex justify-between items-start sm:items-center p-3 border rounded-md gap-2 hover:bg-muted/50 ${
                    item.completed ? "opacity-60" : ""
                  } ${index === currentAgendaItemIndex && isRunning ? "border-primary ring-1 ring-primary" : ""}`} // Utilise l'état synchronisé
                >
                  <div className="flex items-center gap-3 flex-1 overflow-hidden">
                    <Button
                      variant={item.completed ? "secondary" : "ghost"}
                      size="icon"
                      className={`h-6 w-6 flex-shrink-0 ${item.completed ? "text-green-600 hover:text-green-700" : "text-muted-foreground hover:text-foreground"}`}
                      onClick={() => markAgendaItemComplete(item.id)}
                      aria-label={item.completed ? t.markAsNotCompleted : t.markAsCompleted}
                      disabled={isLoading}
                    >
                      <CheckCircle2 className="h-5 w-5" />
                    </Button>
                    <div className="flex-1 overflow-hidden">
                      <p className={`font-medium truncate ${item.completed ? "line-through" : ""}`} title={item.title}>
                        {item.title}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {item.duration} {t.minutes}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 flex-shrink-0">
                    {index === currentAgendaItemIndex && isRunning && (
                      <Badge variant="default" className="text-xs whitespace-nowrap">
                        {t.current}
                      </Badge>
                    )}
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => removeAgendaItem(item.id)}
                      className="h-8 w-8 text-destructive"
                      disabled={isLoading}
                      aria-label={`${t.delete} ${item.title || `(${t.noTitle})`}`}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {/* Add Agenda Item Form */}
             <div className="border-t pt-4">
                 <h4 className="font-medium mb-3">{t.addAgendaItem}</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <Label htmlFor="agenda-title">{t.title} <span className="text-red-500">*</span></Label>
                    <Input
                    id="agenda-title"
                    placeholder={t.agendaItemTitle}
                    value={newAgendaTitle}
                    onChange={(e) => setNewAgendaTitle(e.target.value)}
                    disabled={isLoading}
                    />
                </div>
                <div>
                    <Label htmlFor="agenda-duration">
                    {t.duration} ({t.minutes})
                    </Label>
                    <Input
                    id="agenda-duration"
                    type="number"
                    min="1"
                    max="180"
                    step="1"
                    value={newAgendaDuration}
                    onChange={(e) => setNewAgendaDuration(Math.max(1, Number.parseInt(e.target.value) || 10))}
                    disabled={isLoading}
                    />
                </div>
                </div>

                <Button onClick={addAgendaItem} disabled={newAgendaTitle.trim() === "" || isLoading}>
                <Plus className="h-4 w-4 mr-2" />
                {t.addAgendaItem}
                </Button>
             </div>
          </div>
        </div>
      </TabsContent>

      {/* === Notes Tab === */}
      <TabsContent value="notes">
        <div className="space-y-6">
          <div>
            <Label htmlFor="meeting-notes" className="mb-2 block">
              {t.meetingNotes}
            </Label>
            <Textarea
              id="meeting-notes"
              placeholder={t.enterNotesHere}
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="min-h-[300px] text-sm"
              disabled={isLoading}
            />
             <p className="text-xs text-muted-foreground mt-1">{t.notesAreAutoSaved}</p>
          </div>
        </div>
      </TabsContent>
    </Tabs>
  )

  return (
    <>
      <Card className="w-full max-w-6xl mx-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                {t.meetingTimer}
                {isLoading && <span className="text-xs text-muted-foreground">({t.loading})</span>}
              </CardTitle>
              {activeMeetingId && !isLoading && (
                <p className="text-sm text-muted-foreground">{t.currentMeeting}: {meetingTitle || `(${t.noTitle})`}</p>
              )}
            </div>
            <ToolControls
              onFullscreenToggle={setIsToolFullscreen}
              variant="compact"
            />
          </div>
        </CardHeader>
      <CardContent>
        {meetingContent}
      </CardContent>

      {/* === Footer Actions === */}
      <CardFooter className="flex flex-col sm:flex-row justify-between items-center gap-4 border-t pt-6">
        <div className="flex flex-wrap justify-center sm:justify-start gap-2">
          {/* New Meeting Button */}
          <Button variant="default" onClick={startNewMeeting} disabled={isLoading}>
            <Plus className="h-4 w-4 mr-2" />
            {t.newMeeting}
          </Button>
          {/* Save Meeting Button */}
          <Button
            variant="outline"
            onClick={() => saveMeetingInternal(activeMeetingId)} // Call internal directly
            disabled={!activeMeetingId || meetingTitle.trim() === '' || isLoading}
          >
            <Save className="h-4 w-4 mr-2" />
            {t.saveMeeting}
          </Button>
          {/* Export Button */}
          <Button variant="outline" onClick={exportMeetingData} disabled={!activeMeetingId || isLoading}>
            <Download className="h-4 w-4 mr-2" />
            {t.export} (.json)
          </Button>
        </div>
        {/* Back to Timer Button */}
        {activeTab !== 'timer' && (
            <Button variant="ghost" onClick={() => setActiveTab("timer")}>
                <Clock className="h-4 w-4 mr-2" />
                {t.backToTimer}
            </Button>
        )}
      </CardFooter>
    </Card>

    {/* Wrapper plein écran */}
    <ToolFullscreenWrapper
      isFullscreen={isToolFullscreen}
      onClose={() => setIsToolFullscreen(false)}
      toolName={t.meetingTimer}
    >
      <Card className="w-full border-0 shadow-none bg-transparent">
        <CardContent>
          {meetingContent}
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row justify-between items-center gap-4 border-t pt-6">
          <div className="flex flex-wrap justify-center sm:justify-start gap-2">
            {/* New Meeting Button */}
            <Button variant="default" onClick={startNewMeeting} disabled={isLoading}>
              <Plus className="h-4 w-4 mr-2" />
              {t.newMeeting}
            </Button>
            {/* Save Meeting Button */}
            <Button
              variant="outline"
              onClick={() => saveMeetingInternal(activeMeetingId)} // Call internal directly
              disabled={!activeMeetingId || meetingTitle.trim() === '' || isLoading}
            >
              <Save className="h-4 w-4 mr-2" />
              {t.saveMeeting}
            </Button>
            {/* Export Button */}
            <Button variant="outline" onClick={exportMeetingData} disabled={!activeMeetingId || isLoading}>
              <Download className="h-4 w-4 mr-2" />
              {t.export} (.json)
            </Button>
          </div>
          {/* Back to Timer Button */}
          {activeTab !== 'timer' && (
              <Button variant="ghost" onClick={() => setActiveTab("timer")}>
                  <Clock className="h-4 w-4 mr-2" />
                  {t.backToTimer}
              </Button>
          )}
        </CardFooter>
      </Card>
    </ToolFullscreenWrapper>
  </>
  )
}
