import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { LanguageProvider } from "@/components/language-provider"
import { SoundProvider } from "@/components/sound-provider"
import { Sidebar } from "@/components/sidebar"
import { Footer } from "@/components/footer"
import { AdSpace } from "@/components/ad-space"
import { RTLProvider } from "@/components/rtl-provider"
import { HtmlAttributes } from "@/components/html-attributes"
import { DynamicPageTitle } from "@/components/dynamic-page-title"
import { GoogleAnalytics } from "@/components/google-analytics"
import { PWAInstaller, PWAUpdater } from "@/components/pwa-installer"
import { DynamicManifest } from "@/components/dynamic-manifest"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Timer Kit - Professional Online Time Management Tools",
  description: "Free online time management tools: stopwatch, countdown timer, world clock, pomodoro timer and more for improved productivity.",
  keywords: "time management, online timer, stopwatch, countdown, pomodoro, world clock, productivity tools",
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://timerkit.com/',
    siteName: 'Timer Kit',
    title: 'Timer Kit - Professional Online Time Management Tools',
    description: 'Free online time management tools: stopwatch, countdown timer, world clock, pomodoro timer and more for improved productivity.',
    images: [
      {
        url: 'https://timerkit.com/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Timer Kit - Professional Time Management Tools',
      }
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Timer Kit - Professional Online Time Management Tools',
    description: 'Free online time management tools: stopwatch, countdown timer, world clock, pomodoro timer and more for improved productivity.',
    images: ['https://timerkit.com/og-image.jpg'],
  },
  alternates: {
    canonical: 'https://timerkit.com',
    languages: {
      'en': 'https://timerkit.com/en',
      'fr': 'https://timerkit.com/fr',
      'es': 'https://timerkit.com/es',
      'de': 'https://timerkit.com/de',
      'it': 'https://timerkit.com/it',
      'pt': 'https://timerkit.com/pt',
      'nl': 'https://timerkit.com/nl',
      'pl': 'https://timerkit.com/pl',
      'uk': 'https://timerkit.com/uk',
      'tr': 'https://timerkit.com/tr',
      'ru': 'https://timerkit.com/ru',
      'ar': 'https://timerkit.com/ar',
      'he': 'https://timerkit.com/he',
      'fa': 'https://timerkit.com/fa',
      'hi': 'https://timerkit.com/hi',
      'bn': 'https://timerkit.com/bn',
      'te': 'https://timerkit.com/te',
      'ta': 'https://timerkit.com/ta',
      'mr': 'https://timerkit.com/mr',
      'gu': 'https://timerkit.com/gu',
      'kn': 'https://timerkit.com/kn',
      'ml': 'https://timerkit.com/ml',
      'pa': 'https://timerkit.com/pa',
      'ur': 'https://timerkit.com/ur',
      'id': 'https://timerkit.com/id',
      'ms': 'https://timerkit.com/ms',
      'th': 'https://timerkit.com/th',
      'vi': 'https://timerkit.com/vi',
      'km': 'https://timerkit.com/km',
      'my': 'https://timerkit.com/my',
      'zh': 'https://timerkit.com/zh',
      'ja': 'https://timerkit.com/ja',
      'ko': 'https://timerkit.com/ko',
      'el': 'https://timerkit.com/el',
      'bg': 'https://timerkit.com/bg',
      'cs': 'https://timerkit.com/cs',
      'sk': 'https://timerkit.com/sk',
      'hu': 'https://timerkit.com/hu',
      'ro': 'https://timerkit.com/ro',
      'hr': 'https://timerkit.com/hr',
      'sr': 'https://timerkit.com/sr',
      'bs': 'https://timerkit.com/bs',
      'sl': 'https://timerkit.com/sl',
      'mk': 'https://timerkit.com/mk',
      'et': 'https://timerkit.com/et',
      'lv': 'https://timerkit.com/lv',
      'lt': 'https://timerkit.com/lt',
      'da': 'https://timerkit.com/da',
      'fi': 'https://timerkit.com/fi',
      'nb': 'https://timerkit.com/nb',
      'sv': 'https://timerkit.com/sv',
      'ca': 'https://timerkit.com/ca',
      'gl': 'https://timerkit.com/gl',
      'eu': 'https://timerkit.com/eu',
      'af': 'https://timerkit.com/af',
      'sw': 'https://timerkit.com/sw',
      'am': 'https://timerkit.com/am',
      'ka': 'https://timerkit.com/ka',
      'hy': 'https://timerkit.com/hy',
      'az': 'https://timerkit.com/az',
      'uz': 'https://timerkit.com/uz',
      'kk': 'https://timerkit.com/kk',
      'tg': 'https://timerkit.com/tg',
      'tk': 'https://timerkit.com/tk',
      'ky': 'https://timerkit.com/ky',
    }
  }
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html suppressHydrationWarning>
      <head>
        <GoogleAnalytics />
        {/* La balise canonical est gérée par les métadonnées de chaque page */}
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
        <meta name="robots" content="index, follow" />
        <meta name="author" content="Timer Kit" />
        <meta name="theme-color" content="#3b82f6" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Timer Kit" />
        <link rel="apple-touch-icon" href="/icons/icon-192x192.png" />
        <link rel="manifest" href="/api/manifest" />
      </head>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <LanguageProvider>
            <SoundProvider>
              <RTLProvider>
                {/* Client components to set HTML attributes and page title based on URL */}
                <HtmlAttributes />
                <DynamicPageTitle />

                <div className="flex flex-col min-h-screen w-screen">
                  <Sidebar />
                  <div className="flex flex-1 overflow-hidden mx-[-1rem] sm:mx-0">
                    {/* Left sidebar with ads */}
                    <div className="hidden md:block w-64 p-4 border-r rtl:border-l rtl:border-r-0">
                      <AdSpace position="left" />
                    </div>

                    {/* Main content */}
                    <div className="flex-1 flex flex-col overflow-hidden w-screen sm:w-auto">
                      <main className="flex-1 overflow-auto px-1 md:p-4 w-full rtl:text-right">{children}</main>

                      {/* Bottom ad space */}
                      <div className="p-4 border-t">
                        <AdSpace position="bottom" />
                      </div>
                    </div>

                    {/* Right sidebar with ads */}
                    <div className="hidden md:block w-64 p-4 border-l rtl:border-r rtl:border-l-0">
                      <AdSpace position="right" />
                    </div>
                  </div>
                  <PWAUpdater />
                  <DynamicManifest />
                </div>
              </RTLProvider>
            </SoundProvider>
          </LanguageProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
