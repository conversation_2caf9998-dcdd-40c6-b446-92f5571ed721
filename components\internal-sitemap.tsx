"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { getTranslations } from "@/lib/i18n/translations"
import { getTranslatedRoute } from "@/lib/i18n/route-translations"
import Link from "next/link"
import { Clock, Timer, Globe, ListTodo, Activity, Users, FileText, Home } from "lucide-react"

interface InternalSitemapProps {
  lang?: string
  compact?: boolean
}

export function InternalSitemap({ lang = "en", compact = false }: InternalSitemapProps) {
  const t = getTranslations(lang)

  // Helper function to get localized links
  const getLocalizedLink = (route: string) => {
    const translatedRoute = getTranslatedRoute(lang, route)
    return `/${lang}/${translatedRoute}`
  }

  const sitemapSections = [
    {
      title: t.timeTools,
      icon: <Clock className="h-5 w-5" />,
      links: [
        {
          name: t.timerStopwatch,
          href: getLocalizedLink("timer"),
          description: compact ? undefined : t.timerDescription
        },
        {
          name: t.countdown,
          href: getLocalizedLink("countdown"),
          description: compact ? undefined : t.countdownDescription
        },
        {
          name: t.worldClock,
          href: getLocalizedLink("world-clock"),
          description: compact ? undefined : t.worldClockDescription
        },
        {
          name: t.intervals,
          href: getLocalizedLink("intervals"),
          description: compact ? undefined : t.intervalsDescription
        },
        {
          name: t.pomodoro,
          href: getLocalizedLink("pomodoro"),
          description: compact ? undefined : t.pomodoroDescription
        }
      ]
    },
    {
      title: t.productivityTools,
      icon: <ListTodo className="h-5 w-5" />,
      links: [
        {
          name: t.todoList,
          href: getLocalizedLink("todo"),
          description: compact ? undefined : t.todoDescription
        },
        {
          name: t.timeTracking,
          href: getLocalizedLink("time-tracking"),
          description: compact ? undefined : t.timeTrackingDescription
        }
      ]
    },
    {
      title: t.professionalTools,
      icon: <Users className="h-5 w-5" />,
      links: [
        {
          name: t.meetingTimer,
          href: getLocalizedLink("meeting-timer"),
          description: compact ? undefined : t.meetingTimerDescription
        },
        {
          name: t.timeBilling,
          href: getLocalizedLink("time-billing"),
          description: compact ? undefined : t.timeBillingDescription
        }
      ]
    },
    {
      title: t.fitnessTools,
      icon: <Activity className="h-5 w-5" />,
      links: [
        {
          name: t.workoutIntervals,
          href: getLocalizedLink("workout-intervals"),
          description: compact ? undefined : t.workoutIntervalsDescription
        },
        {
          name: t.exerciseTemplates,
          href: getLocalizedLink("exercise-templates"),
          description: compact ? undefined : t.exerciseTemplatesDescription
        }
      ]
    }
  ]

  if (compact) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <Link
            href={`/${lang}`}
            className="inline-flex items-center gap-2 text-lg font-semibold text-primary hover:underline"
          >
            <Home className="h-5 w-5" />
            {t.home}
          </Link>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {sitemapSections.map((section, index) => (
            <div key={index} className="space-y-3">
              <h3 className="flex items-center gap-2 font-semibold text-sm uppercase tracking-wide text-muted-foreground">
                {section.icon}
                {section.title}
              </h3>
              <ul className="space-y-2">
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <Link
                      href={link.href}
                      className="text-sm text-foreground hover:text-primary transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div className="text-center">
        <Link
          href={`/${lang}`}
          className="inline-flex items-center gap-2 text-2xl font-bold text-primary hover:underline"
        >
          <Home className="h-6 w-6" />
          {t.home}
        </Link>
        <p className="text-muted-foreground mt-2">{t.homePageDescription}</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {sitemapSections.map((section, index) => (
          <Card key={index}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {section.icon}
                {section.title}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {section.links.map((link, linkIndex) => (
                  <div key={linkIndex} className="border-l-2 border-primary/20 pl-4">
                    <Link
                      href={link.href}
                      className="block font-medium text-primary hover:underline"
                    >
                      {link.name}
                    </Link>
                    {link.description && (
                      <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                        {link.description}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
