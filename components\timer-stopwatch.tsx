"use client"

import { useState, useEffect, useRef } from "react"
import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { getTranslations } from "@/lib/i18n/translations"
import { Play, Pause, RefreshCw, Bell } from "lucide-react"
import { useSound } from "@/components/sound-provider"
import { useRTL } from "@/hooks/useRTL"
import { useLanguage } from "@/components/language-provider"
import { ToolControls } from "@/components/tool-controls"
import { ToolFullscreenWrapper } from "@/components/tool-fullscreen-wrapper"
import { trackToolUsage, trackTimerEvent } from "@/components/google-analytics"

interface TimerStopwatchProps {
  lang: string;
  isStandalone?: boolean; // Indique si le composant est utilisé dans la page timer-stopwatch
}

export function TimerStopwatch({ lang, isStandalone = false }: TimerStopwatchProps) {
  const t = getTranslations(lang)
  const { language } = useLanguage()
  const isRTL = useRTL(language || lang)

  // Inverser l'ordre des boutons pour les langues RTL
  const shouldReverseButtons = isRTL

  // Pour les laps, nous n'inversons pas l'ordre car cela cause des problèmes
  const [isRunning, setIsRunning] = useState(false)
  const [time, setTime] = useState(0) // Elapsed time in milliseconds
  const [laps, setLaps] = useState<number[]>([])
  const [isToolFullscreen, setIsToolFullscreen] = useState(false)
  // const intervalRef = useRef<NodeJS.Timeout | null>(null) // No longer using setInterval
  const animationFrameRef = useRef<number | null>(null) // Ref for requestAnimationFrame ID
  const startTimeRef = useRef<number>(0) // Timestamp when the timer started/resumed
  const elapsedTimeBeforePauseRef = useRef<number>(0) // Time elapsed before the last pause
  const originalTitle = useRef<string>("")

  // Utiliser le hook useSound au lieu de gérer le son directement
  const { playSound } = useSound()

  // Sauvegarder le titre original de la page au chargement du composant
  useEffect(() => {
    originalTitle.current = document.title
    return () => {
      document.title = originalTitle.current
    }
  }, [])

  // Mettre à jour le titre de l'onglet du navigateur avec le temps actuel
  useEffect(() => {
    if (isRunning || time > 0) {
      document.title = `${formatTime(time)} - ${t.stopwatch || "Chronomètre"}`
    } else {
      document.title = originalTitle.current
    }
  }, [time, isRunning, t])

  // Supprimer les useEffect et les refs liés à Howler

  // Use requestAnimationFrame for smooth UI updates
  useEffect(() => {
    const updateTimer = () => {
      if (!isRunning) return // Stop the loop if not running

      // Calculate elapsed time based on current time and start time
      const now = Date.now()
      const elapsedSinceStart = now - startTimeRef.current
      setTime(elapsedTimeBeforePauseRef.current + elapsedSinceStart)

      // Request the next frame
      animationFrameRef.current = requestAnimationFrame(updateTimer)
    }

    if (isRunning) {
      // Record the start time when the timer begins or resumes
      startTimeRef.current = Date.now()
      // Start the animation loop
      animationFrameRef.current = requestAnimationFrame(updateTimer)
    } else {
      // When pausing, cancel the animation frame
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
        animationFrameRef.current = null
      }
      // Record the total elapsed time accurately upon pausing
      if (startTimeRef.current !== 0) { // Avoid recording if paused immediately after reset
        const elapsedSinceLastStart = Date.now() - startTimeRef.current
        elapsedTimeBeforePauseRef.current += elapsedSinceLastStart
        // Ensure the final time is accurately set when pausing
        setTime(elapsedTimeBeforePauseRef.current)
        startTimeRef.current = 0 // Reset start time after calculating pause time
      }
    }

    // Cleanup function to cancel animation frame if component unmounts while running
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [isRunning]) // Dependency array only needs isRunning

  const toggleTimer = () => {
    const newRunningState = !isRunning
    setIsRunning(newRunningState)

    // Track timer events in Google Analytics
    if (newRunningState) {
      trackTimerEvent('stopwatch', time, 'start')
      trackToolUsage('stopwatch', 'start')
    } else {
      trackTimerEvent('stopwatch', time, 'pause')
      trackToolUsage('stopwatch', 'pause')
    }
  }

  const resetTimer = () => {
    // Track reset event before resetting
    trackTimerEvent('stopwatch', time, 'reset')
    trackToolUsage('stopwatch', 'reset')

    setIsRunning(false)
    setTime(0)
    setLaps([])
    elapsedTimeBeforePauseRef.current = 0 // Reset paused time
    startTimeRef.current = 0 // Reset start time ref
    // Cancel any pending animation frame on reset
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current)
      animationFrameRef.current = null
    }
    // Restaurer le titre original
    document.title = originalTitle.current
  }

  // Modifier la fonction addLap pour ajouter une gestion d'erreur
  const addLap = () => {
    setLaps([...laps, time])

    // Track lap event
    trackToolUsage('stopwatch', 'lap')
    trackTimerEvent('stopwatch', time, 'lap')

    // Ajouter un petit délai pour s'assurer que le son se déclenche
    setTimeout(() => {
      try {
        playSound("bell")
      } catch (error) {
        console.warn("Could not play sound:", error)
      }
    }, 10)
  }

  const formatTime = (ms: number) => {
    const minutes = Math.floor(ms / 60000)
    const seconds = Math.floor((ms % 60000) / 1000)
    const milliseconds = Math.floor((ms % 1000) / 10)

    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}.${milliseconds.toString().padStart(2, "0")}`
  }

  const timerContent = (
    <div className="flex flex-col items-center justify-center py-12">
      <motion.div
        className="text-7xl md:text-8xl font-bold font-mono mb-12"
        // key={time} // Removed key to prevent flickering on every time update
        initial={{ scale: 0.95 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.1 }}
      >
        {formatTime(time)}
      </motion.div>

      <div className="flex gap-6 mb-12" data-rtl-flex="true">
        <Button
          onClick={toggleTimer}
          size="lg"
          className="w-32 h-32 rounded-full"
          variant={isRunning ? "destructive" : "default"}
        >
          {isRunning ? <Pause className="h-10 w-10" /> : <Play className="h-10 w-10" />}
        </Button>

        <Button
          onClick={resetTimer}
          size="lg"
          variant="outline"
          className="w-20 h-20 rounded-full"
          disabled={time === 0}
        >
          <RefreshCw className="h-8 w-8" />
        </Button>

        <Button
          onClick={addLap}
          size="lg"
          variant="outline"
          className="w-20 h-20 rounded-full"
          disabled={!isRunning && time === 0}
        >
          <Bell className="h-8 w-8" />
        </Button>
      </div>

      {laps.length > 0 && (
        <div className="w-full max-h-60 overflow-auto border rounded-lg p-4" dir={isRTL ? 'rtl' : 'ltr'} style={{ textAlign: isRTL ? 'right' : 'left' }}>
          <h3 className="font-medium mb-2">{t.laps}</h3>
          <ul className="space-y-2">
            {laps.map((lap, index) => (
              <li key={index} className="py-1 border-b last:border-0" style={{
                display: 'flex',
                justifyContent: 'space-between',
                direction: isRTL ? 'rtl' : 'ltr'
              }}>
                <div style={{ textAlign: isRTL ? 'right' : 'left' }}>
                  {t.lap} {index + 1}
                </div>
                <div className="font-mono" style={{ textAlign: isRTL ? 'left' : 'right' }}>
                  {formatTime(lap)}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  )

  return (
    <>
      <Card className="w-full max-w-4xl mx-auto overflow-hidden">
        <CardContent>
          {/* Contrôles d'outils en haut à droite */}
          <div className="flex justify-end pt-4 pb-2">
            <ToolControls
              onFullscreenToggle={setIsToolFullscreen}
              variant="compact"
            />
          </div>

          {timerContent}
        </CardContent>
      </Card>

      {/* Wrapper plein écran */}
      <ToolFullscreenWrapper
        isFullscreen={isToolFullscreen}
        onClose={() => setIsToolFullscreen(false)}
        toolName={t.stopwatch}
      >
        <Card className="w-full border-0 shadow-none bg-transparent">
          <CardContent>
            {timerContent}
          </CardContent>
        </Card>
      </ToolFullscreenWrapper>
    </>
  )
}
